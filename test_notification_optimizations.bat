@echo off
REM TJ_BatteryOne Notification Service Optimization Testing Script
REM This script verifies the optimized notification implementation using ADB commands
REM ADB Path: E:\IDE\Android\SDK\platform-tools\adb.exe
REM Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo ========================================
echo TJ_BatteryOne Notification Optimization Testing
echo ========================================
echo.

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set PACKAGE_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

REM Check if ADB is available
if not exist "%ADB_PATH%" (
    echo ERROR: ADB not found at %ADB_PATH%
    echo Please verify the ADB path is correct.
    pause
    exit /b 1
)

REM Check if device is connected
echo Checking device connection...
%ADB_PATH% devices | findstr "device" > nul
if errorlevel 1 (
    echo ERROR: No Android device connected or ADB not authorized
    echo Please connect your device and enable USB debugging
    pause
    exit /b 1
)

echo Device connected successfully!
echo.

REM Test 1: Verify Service Consolidation
echo ========================================
echo Test 1: Service Consolidation Verification
echo ========================================
echo Checking which battery services are running...
echo.

echo Expected: Only CoreBatteryStatsService should be running
echo Checking for CoreBatteryStatsService...
%ADB_PATH% shell dumpsys activity services | findstr "CoreBatteryStatsService"
if errorlevel 1 (
    echo WARNING: CoreBatteryStatsService not found
) else (
    echo ✓ CoreBatteryStatsService is running
)

echo.
echo Checking for legacy services (should NOT be running)...
echo Checking for BatteryStatusService...
%ADB_PATH% shell dumpsys activity services | findstr "BatteryStatusService"
if errorlevel 1 (
    echo ✓ BatteryStatusService is NOT running (correct)
) else (
    echo ⚠ WARNING: BatteryStatusService is still running (should be disabled)
)

echo Checking for BatteryMonitorService...
%ADB_PATH% shell dumpsys activity services | findstr "BatteryMonitorService"
if errorlevel 1 (
    echo ✓ BatteryMonitorService is NOT running (correct)
) else (
    echo ⚠ WARNING: BatteryMonitorService is still running (should be disabled)
)

echo Checking for NewChargeMonitorService...
%ADB_PATH% shell dumpsys activity services | findstr "NewChargeMonitorService"
if errorlevel 1 (
    echo ✓ NewChargeMonitorService is NOT running (correct)
) else (
    echo ⚠ WARNING: NewChargeMonitorService is still running (should be disabled)
)

echo.
pause

REM Test 2: Monitor Notification Updates
echo ========================================
echo Test 2: Notification Update Monitoring
echo ========================================
echo Starting notification update monitoring...
echo This will show real-time notification updates with timing information
echo Press Ctrl+C to stop monitoring
echo.

echo Starting logcat monitoring for notification updates...
%ADB_PATH% logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D" | findstr /C:"Updating service notification" /C:"Generated notification content" /C:"Adaptive" /C:"interval"

pause

REM Test 3: Battery Status Monitoring
echo ========================================
echo Test 3: Battery Status and Performance
echo ========================================
echo Monitoring battery status and app performance...
echo.

echo Current battery status:
%ADB_PATH% shell dumpsys battery

echo.
echo App memory usage:
%ADB_PATH% shell dumpsys meminfo %PACKAGE_ID% | findstr "TOTAL"

echo.
echo CPU usage (top processes):
%ADB_PATH% shell top -n 1 | findstr "%PACKAGE_ID%"

echo.
pause

REM Test 4: Notification Content Verification
echo ========================================
echo Test 4: Notification Content Testing
echo ========================================
echo Testing notification content and caching...
echo This will monitor notification content generation
echo.

echo Monitoring notification content generation...
%ADB_PATH% logcat -s "ChargeNotificationManager:V" | findstr /C:"Generated notification content" /C:"cached"

pause

REM Test 5: Charging State Change Testing
echo ========================================
echo Test 5: Charging State Change Testing
echo ========================================
echo.
echo MANUAL TEST INSTRUCTIONS:
echo 1. Connect your device charger
echo 2. Observe charging started notification
echo 3. Disconnect charger
echo 4. Observe charging stopped notification
echo 5. Monitor the logs below for state changes
echo.

echo Monitoring charging state changes...
%ADB_PATH% logcat -s "UnifiedBatteryNotificationService:D" | findstr /C:"Charging state changed" /C:"started charging" /C:"stopped charging"

pause

REM Test 6: Performance Benchmarking
echo ========================================
echo Test 6: Performance Benchmarking
echo ========================================
echo Running performance tests...
echo.

echo Measuring app startup time...
%ADB_PATH% shell am start -W %PACKAGE_ID%/.activity.main.MainActivity

echo.
echo Waiting 5 seconds for app to stabilize...
timeout /t 5 /nobreak > nul

echo.
echo Memory usage after startup:
%ADB_PATH% shell dumpsys meminfo %PACKAGE_ID% | findstr "TOTAL\|Native\|Dalvik"

echo.
echo CPU usage during normal operation:
%ADB_PATH% shell top -n 3 | findstr "%PACKAGE_ID%"

echo.
pause

REM Test 7: Adaptive Update Interval Testing
echo ========================================
echo Test 7: Adaptive Update Interval Testing
echo ========================================
echo Testing adaptive notification update intervals...
echo.

echo MANUAL TEST INSTRUCTIONS:
echo 1. Ensure device screen is ON
echo 2. Connect charger (should see faster updates ~15s)
echo 3. Turn screen OFF (should see slower updates ~60s)
echo 4. Turn screen ON again (should see normal updates ~45s)
echo 5. Monitor timing in logs below
echo.

echo Monitoring adaptive update intervals...
%ADB_PATH% logcat -s "UnifiedBatteryNotificationService:D" | findstr /C:"interval" /C:"Screen turned" /C:"Updating service notification"

pause

REM Test 8: Target Percentage Alert Testing
echo ========================================
echo Test 8: Target Percentage Alert Testing
echo ========================================
echo.
echo MANUAL TEST INSTRUCTIONS:
echo 1. Set a target percentage in the app (e.g., 80%)
echo 2. Charge your device towards that percentage
echo 3. Watch for target percentage alert notification
echo 4. Monitor logs below for alert triggers
echo.

echo Monitoring target percentage alerts...
%ADB_PATH% logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D" | findstr /C:"Target charge" /C:"reached" /C:"showTargetChargeReachedNotification"

pause

echo ========================================
echo Testing Complete!
echo ========================================
echo.
echo SUMMARY OF TESTS:
echo ✓ Service consolidation verification
echo ✓ Notification update monitoring
echo ✓ Battery status and performance check
echo ✓ Notification content verification
echo ✓ Charging state change testing
echo ✓ Performance benchmarking
echo ✓ Adaptive update interval testing
echo ✓ Target percentage alert testing
echo.
echo Review the logs above to verify:
echo - Only CoreBatteryStatsService is running
echo - Notification updates occur at appropriate intervals
echo - Content caching is working
echo - Performance is optimal
echo - All notification types function correctly
echo.
echo For detailed analysis, check the Android Studio logcat
echo or use: adb logcat -s "UnifiedBatteryNotificationService" "ChargeNotificationManager"
echo.
pause
