# TJ_BatteryOne Notification Optimization - ADB Quick Reference

## Essential Commands for Immediate Testing

### 🔧 Setup Commands
```bash
# Check device connection
E:\IDE\Android\SDK\platform-tools\adb.exe devices

# Start the app
E:\IDE\Android\SDK\platform-tools\adb.exe shell am start -W com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity
```

### 🔍 Service Verification (Most Important)
```bash
# Check if only CoreBatteryStatsService is running
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "CoreBatteryStatsService"

# Verify legacy services are NOT running (should return nothing)
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "BatteryStatusService\|BatteryMonitorService\|NewChargeMonitorService"
```

### 📊 Real-Time Monitoring
```bash
# Monitor all notification activity
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D"

# Monitor adaptive intervals specifically
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "interval\|Screen\|Updating"

# Monitor forced updates
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "Forcing"
```

### ⚡ Performance Check
```bash
# Memory usage
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys meminfo com.fc.p.tj.charginganimation.batterycharging.chargeeffect | findstr "TOTAL"

# CPU usage
E:\IDE\Android\SDK\platform-tools\adb.exe shell top -n 1 | findstr "com.fc.p.tj.charginganimation"

# Battery status
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys battery
```

### 🎯 Quick Test Scenarios

#### Test 1: Service Consolidation (30 seconds)
1. Run: `adb shell dumpsys activity services | findstr "CoreBatteryStatsService"`
2. **Expected**: Should find CoreBatteryStatsService
3. Run: `adb shell dumpsys activity services | findstr "BatteryStatusService\|BatteryMonitorService\|NewChargeMonitorService"`
4. **Expected**: Should return nothing (no legacy services)

#### Test 2: Adaptive Intervals (2 minutes)
1. Start monitoring: `adb logcat -s "UnifiedBatteryNotificationService:D" | findstr "interval"`
2. Connect charger → Look for ~15 second intervals
3. Turn screen off → Look for ~60 second intervals
4. Turn screen on → Look for ~45 second intervals

#### Test 3: Forced Updates (1 minute)
1. Start monitoring: `adb logcat -s "UnifiedBatteryNotificationService:D" | findstr "Forcing"`
2. Connect/disconnect charger
3. **Expected**: "Forcing notification update - charging state changed"

#### Test 4: Content Accuracy (30 seconds)
1. Monitor: `adb logcat -s "ChargeNotificationManager:D" | findstr "Now:"`
2. **Expected**: "Now: X.X mA (X.X W) • Temp: X.X°C ⚡" (when charging)

## 🚨 Critical Success Indicators

### ✅ PASS Criteria
- **Service Consolidation**: Only CoreBatteryStatsService running
- **Adaptive Timing**: Different intervals for charging/screen states
- **Forced Updates**: Immediate updates on state changes
- **Content Format**: Proper "Now: X.X mA (X.X W) • Temp: X.X°C" format
- **Performance**: <20MB memory, <2% CPU

### ❌ FAIL Indicators
- Legacy services still running
- Fixed 30-second intervals (no adaptation)
- No forced updates on charger connect/disconnect
- Missing charging indicator (⚡) in notifications
- High memory usage (>30MB) or CPU (>5%)

## 🔧 Troubleshooting

### If CoreBatteryStatsService not found:
```bash
# Force stop and restart app
E:\IDE\Android\SDK\platform-tools\adb.exe shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
E:\IDE\Android\SDK\platform-tools\adb.exe shell am start com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity
```

### If no logs appearing:
```bash
# Clear logcat buffer and restart monitoring
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -c
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService" "ChargeNotificationManager"
```

### If legacy services still running:
```bash
# Check app version and restart device
E:\IDE\Android\SDK\platform-tools\adb.exe shell pm dump com.fc.p.tj.charginganimation.batterycharging.chargeeffect | findstr "versionName"
E:\IDE\Android\SDK\platform-tools\adb.exe reboot
```

## 📋 5-Minute Quick Test Checklist

1. **[30s] Device Setup**
   - [ ] Device connected: `adb devices`
   - [ ] App running: `adb shell am start -W com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity`

2. **[60s] Service Check**
   - [ ] CoreBatteryStatsService running
   - [ ] No legacy services running

3. **[120s] Adaptive Intervals**
   - [ ] Start monitoring intervals
   - [ ] Test charging state (15s intervals)
   - [ ] Test screen off (60s intervals)

4. **[60s] Forced Updates**
   - [ ] Monitor forced updates
   - [ ] Connect/disconnect charger
   - [ ] Verify immediate updates

5. **[30s] Performance**
   - [ ] Check memory usage (<20MB)
   - [ ] Check CPU usage (<2%)

**Total Time**: 5 minutes  
**Pass Criteria**: All checkboxes ✅

## 📞 Support Commands

```bash
# Collect logs for analysis
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -d > tj_battery_logs.txt

# Get app info
E:\IDE\Android\SDK\platform-tools\adb.exe shell pm dump com.fc.p.tj.charginganimation.batterycharging.chargeeffect | findstr "versionName\|versionCode"

# Clear app data (if needed)
E:\IDE\Android\SDK\platform-tools\adb.exe shell pm clear com.fc.p.tj.charginganimation.batterycharging.chargeeffect
```
