@echo off
REM StatsChargeModule Log Monitoring Script
REM Monitors specific logs for StatsChargeModule components

set ADB_PATH="E:\IDE\Android\SDK\platform-tools\adb.exe"

echo ========================================
echo StatsChargeModule Log Monitor
echo ========================================
echo Monitoring logs for:
echo - CoreBatteryStatsService
echo - StatsChargeRepository  
echo - StatsChargeViewModel
echo - StatsChargeFragment
echo - StatsChargeCache
echo - CalculateSimpleChargeEstimateUseCase
echo.
echo Press Ctrl+C to stop monitoring
echo ========================================
echo.

REM Clear previous logs
%ADB_PATH% logcat -c

REM Monitor logs with specific filters
%ADB_PATH% logcat | findstr /i "StatsCharge CoreBattery STATS_CHARGE CORE_BATTERY ChargeEstimate"
