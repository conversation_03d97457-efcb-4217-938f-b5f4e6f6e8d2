@echo off
echo ========================================
echo TJ_BatteryOne Performance Testing Script
echo ========================================

set ADB_PATH="E:\IDE\Android\SDK\platform-tools\adb.exe"
set PACKAGE_NAME=com.tqhit.battery.one

echo.
echo 1. Checking device connection...
%ADB_PATH% devices
if %errorlevel% neq 0 (
    echo ERROR: No devices connected or ADB not working
    pause
    exit /b 1
)

echo.
echo 2. Installing optimized APK...
%ADB_PATH% install -r "d:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\build\outputs\apk\debug\app-debug.apk"
if %errorlevel% neq 0 (
    echo ERROR: Failed to install APK
    pause
    exit /b 1
)

echo.
echo 3. Force stopping app for cold start test...
%ADB_PATH% shell am force-stop %PACKAGE_NAME%
timeout /t 2 /nobreak > nul

echo.
echo 4. Starting performance monitoring...
start "Performance Monitor" %ADB_PATH% logcat -s PerformanceMonitor BatteryApplication MainActivity

echo.
echo 5. Measuring cold start time...
echo Starting app and measuring launch time...
%ADB_PATH% shell am start -W -n %PACKAGE_NAME%/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo 6. Testing fragment navigation performance...
echo Please manually navigate between fragments in the app
echo Press any key when done testing navigation...
pause > nul

echo.
echo 7. Generating performance report...
echo Force closing app to trigger performance report...
%ADB_PATH% shell am force-stop %PACKAGE_NAME%

echo.
echo 8. Collecting performance logs...
%ADB_PATH% logcat -d -s PerformanceMonitor > performance_results.txt
echo Performance results saved to performance_results.txt

echo.
echo 9. Collecting startup timing logs...
%ADB_PATH% logcat -d | findstr "TotalTime\|WaitTime\|ThisTime" > startup_timing.txt
echo Startup timing saved to startup_timing.txt

echo.
echo ========================================
echo Performance testing completed!
echo Check the following files for results:
echo - performance_results.txt
echo - startup_timing.txt
echo ========================================
pause
