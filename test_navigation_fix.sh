#!/bin/bash

# Test script for navigation bar fix
# Tests the 4-button vs 5-button navigation issue after force stop and restart

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"

echo "=== Navigation Bar Fix Test ==="
echo "Testing 4-button navigation layout after force stop and restart"
echo

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device connected. Please connect a device or start an emulator."
    exit 1
fi

echo "✅ Android device detected"

# Build the app
echo "🔨 Building debug APK..."
if ! ./gradlew assembleDebug > /dev/null 2>&1; then
    echo "❌ Build failed. Please check the build errors."
    exit 1
fi

echo "✅ Build successful"

# Install the app
echo "📱 Installing app..."
if ! adb install -r "$APK_PATH" > /dev/null 2>&1; then
    echo "❌ Installation failed"
    exit 1
fi

echo "✅ App installed successfully"

# Function to check battery status
check_battery_status() {
    local status=$(adb shell dumpsys battery | grep "AC powered" | awk '{print $3}')
    if [ "$status" = "true" ]; then
        echo "🔌 Device is charging"
        return 0
    else
        echo "🔋 Device is not charging"
        return 1
    fi
}

# Function to start logcat monitoring
start_logcat() {
    echo "📊 Starting logcat monitoring..."
    adb logcat -c  # Clear existing logs
    adb logcat -s "DynamicNavigationManager:D" "MainActivity:D" "CoreBatteryStatsProvider:D" > navigation_test.log &
    LOGCAT_PID=$!
    sleep 2
}

# Function to stop logcat monitoring
stop_logcat() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null
        wait $LOGCAT_PID 2>/dev/null
    fi
}

# Function to analyze logs
analyze_logs() {
    echo "📋 Analyzing navigation logs..."
    
    if [ -f "navigation_test.log" ]; then
        echo "--- Key Navigation Events ---"
        grep -E "(Setting up initial state|Total visible menu items|Menu item.*visibility)" navigation_test.log | tail -10
        
        # Check for 5-button issue
        if grep -q "Total visible menu items: 5" navigation_test.log; then
            echo "❌ ISSUE DETECTED: Navigation showing 5 buttons instead of 4"
            return 1
        elif grep -q "Total visible menu items: 4" navigation_test.log; then
            echo "✅ SUCCESS: Navigation showing correct 4 buttons"
            return 0
        else
            echo "⚠️  Could not determine button count from logs"
            return 2
        fi
    else
        echo "❌ No log file found"
        return 1
    fi
}

# Test sequence
echo "🧪 Starting test sequence..."

# Check initial battery status
check_battery_status
INITIAL_CHARGING=$?

# Start monitoring
start_logcat

# Test 1: Force stop and restart
echo "🛑 Force stopping app..."
adb shell am force-stop "$PACKAGE_NAME"
sleep 2

echo "🚀 Launching app..."
adb shell am start -n "$PACKAGE_NAME/com.tqhit.battery.one.activity.main.MainActivity"
sleep 5

# Test 2: Check navigation state
echo "📱 Checking navigation state..."
sleep 3

# Stop monitoring and analyze
stop_logcat
analyze_logs
TEST_RESULT=$?

# Cleanup
echo "🧹 Cleaning up..."
rm -f navigation_test.log

# Summary
echo
echo "=== Test Summary ==="
if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ PASS: Navigation bar shows correct 4-button layout"
    echo "   The fix successfully resolves the 5-button issue"
elif [ $TEST_RESULT -eq 1 ]; then
    echo "❌ FAIL: Navigation bar still shows 5 buttons"
    echo "   The issue persists and needs further investigation"
else
    echo "⚠️  INCONCLUSIVE: Could not determine test result"
    echo "   Manual verification may be needed"
fi

echo
echo "💡 Manual verification steps:"
echo "   1. Open the app on your device"
echo "   2. Count the bottom navigation buttons"
echo "   3. Expected: 4 buttons (Animation, Charge/Discharge, Health, Settings)"
echo "   4. Issue: 5 buttons (Animation, Charge, Discharge, Health, Settings)"

exit $TEST_RESULT
