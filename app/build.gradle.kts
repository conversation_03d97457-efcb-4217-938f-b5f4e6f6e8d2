plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("com.google.devtools.ksp")
    id("com.google.dagger.hilt.android")
    id("com.google.gms.google-services") version "4.4.2"
    id("com.google.firebase.crashlytics") version "3.0.3"
    kotlin("plugin.serialization") version "2.0.21"
//    id("applovin-quality-service")
}

android {
    namespace = "com.tqhit.battery.one"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
        minSdk = 24
        targetSdk = 35
        versionCode = 38
        versionName = "1.2.0.20250601"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            // DebugActivity excluded via src/debug/AndroidManifest.xml
        }
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // DebugActivity excluded via src/release/AndroidManifest.xml
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        viewBinding = true
    }

    dataBinding {
        enable = true
    }

    // Configure Robolectric
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
        }
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.firebase.analytics)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    implementation(libs.adlib)
    implementation(libs.shimmer)
    implementation(libs.play.services.ads)
    implementation(libs.androidx.core.splashscreen)
    implementation (libs.lottie)

    implementation(libs.hilt.android)
    ksp(libs.hilt.android.compiler)

    implementation(libs.sdp.android)
    implementation(libs.ssp.android)
    implementation(libs.mpandroidchart)
    implementation(libs.blurview)
    implementation(libs.dotsindicator)
    implementation(libs.circularseekbar)
    implementation(libs.glide)
    implementation(libs.gson)
    implementation(libs.shimmer)
    implementation(libs.androidx.media3.ui)
    implementation(libs.androidx.media3.exoplayer)

    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Testing dependencies
    testImplementation("io.mockk:mockk:1.13.9")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    testImplementation("org.mockito:mockito-core:5.9.0")
    testImplementation("org.mockito:mockito-junit-jupiter:5.9.0")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.2.1")

    // Robolectric for Android unit tests
    testImplementation("org.robolectric:robolectric:4.11.1")
    testImplementation("androidx.test:core:1.5.0")
    testImplementation("androidx.test:core-ktx:1.5.0")
    testImplementation("androidx.test.ext:junit:1.1.5")

//    implementation("com.applovin:applovin-sdk:+")
//    implementation("com.google.android.ump:user-messaging-platform:3.2.0")
//    implementation("com.applovin.mediation:bidmachine-adapter:+")
//    implementation("com.applovin.mediation:bigoads-adapter:+")
//    implementation("com.applovin.mediation:chartboost-adapter:+")
//    implementation("com.google.android.gms:play-services-base:16.1.0")
//    implementation("com.applovin.mediation:fyber-adapter:+")
//    implementation("com.applovin.mediation:google-ad-manager-adapter:+")
//    implementation("com.applovin.mediation:google-adapter:+")
//    implementation("com.applovin.mediation:hyprmx-adapter:+")
//    implementation("com.applovin.mediation:inmobi-adapter:+")
    implementation("com.squareup.picasso:picasso:2.8")
    implementation("androidx.recyclerview:recyclerview:1.1.0")

//    implementation("com.applovin.mediation:ironsource-adapter:+")
//    implementation("com.applovin.mediation:vungle-adapter:+")
//    implementation("com.applovin.mediation:facebook-adapter:+")
//    implementation("com.applovin.mediation:mintegral-adapter:+")
//    implementation("com.applovin.mediation:moloco-adapter:+")
//    implementation("com.applovin.mediation:bytedance-adapter:+")
//    implementation("com.applovin.mediation:unityads-adapter:+")
//    implementation("com.applovin.mediation:yandex-adapter:+")
}
//applovin {
//    apiKey = "aNW1wt9nTrGBFTGy8Fu23hIPGgNMCNWI4oIQTS3tNclXqrW1AvoPF9ZgbgqiWMgVKx62MNT_B1Es4mJSAwNqc8"
//}