package com.tqhit.battery.one.features.stats.discharge.repository

import android.content.Context
import android.os.PowerManager
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.*
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Tests for DischargeSessionRepository focusing on session restoration and screen time estimation
 */
@OptIn(ExperimentalCoroutinesApi::class)
class DischargeSessionRepositoryTest {

    private lateinit var repository: DischargeSessionRepository
    private lateinit var mockContext: Context
    private lateinit var mockPowerManager: PowerManager
    private lateinit var mockCurrentSessionCache: CurrentSessionCache
    private lateinit var mockSessionManager: SessionManager
    private lateinit var mockGapEstimationCalculator: GapEstimationCalculator
    private lateinit var mockFullSessionReEstimator: FullSessionReEstimator
    private lateinit var mockSessionMetricsCalculator: SessionMetricsCalculator
    private lateinit var mockScreenStateReceiver: ScreenStateReceiver

    @Before
    fun setUp() {
        mockContext = mockk()
        mockPowerManager = mockk()
        mockCurrentSessionCache = mockk()
        mockSessionManager = mockk()
        mockGapEstimationCalculator = mockk()
        mockFullSessionReEstimator = mockk()
        mockSessionMetricsCalculator = mockk()
        mockScreenStateReceiver = mockk()

        // Mock context.getSystemService to return our mock PowerManager
        every { mockContext.getSystemService(Context.POWER_SERVICE) } returns mockPowerManager
        every { mockPowerManager.isInteractive } returns true

        // Mock screen state receiver
        every { mockScreenStateReceiver.register() } just Runs
        every { mockScreenStateReceiver.unregister() } just Runs
        every { mockScreenStateReceiver.forceCheckScreenState() } just Runs
        every { mockScreenStateReceiver.screenStateFlow } returns MutableStateFlow(
            com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent(true)
        )

        // Mock session cache
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns null
        coEvery { mockCurrentSessionCache.saveCurrentSession(any()) } just Runs
        coEvery { mockCurrentSessionCache.clearCurrentSession() } just Runs
    }

    @Test
    fun `test session restoration with active session`() = runTest {
        // Given: A cached active session
        val cachedSession = createTestSession(
            startTime = System.currentTimeMillis() - 3600000, // 1 hour ago
            startPercentage = 80,
            currentPercentage = 75,
            isActive = true,
            screenOnTimeMs = 1800000, // 30 minutes
            screenOffTimeMs = 1800000  // 30 minutes
        )

        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession

        // When: Repository is initialized
        repository = createRepository()

        // Then: Session should be restored
        assertEquals(cachedSession, repository.currentSession.value)
    }

    @Test
    fun `test session restoration with inactive session`() = runTest {
        // Given: A cached inactive session
        val cachedSession = createTestSession(isActive = false)
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession

        // When: Repository is initialized
        repository = createRepository()

        // Then: Session should not be restored
        assertNull(repository.currentSession.value)
    }

    @Test
    fun `test gap estimation on app restart`() = runTest {
        // Given: A cached active session and a live status with battery drop
        val cachedSession = createTestSession(
            startTime = System.currentTimeMillis() - 7200000, // 2 hours ago
            startPercentage = 90,
            currentPercentage = 85, // Cached shows 85%
            isActive = true
        )

        val liveStatus = CoreBatteryStatus(
            percentage = 80, // Live shows 80% (5% drop during gap)
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -300000, // 300mA discharge
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        val reEstimatedSession = cachedSession.copy(
            currentPercentage = 80,
            screenOnTimeMillis = 3600000, // 1 hour estimated
            screenOffTimeMillis = 3600000, // 1 hour estimated
            totalMahConsumed = 150.0 // Estimated consumption
        )

        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession
        coEvery { mockFullSessionReEstimator.reEstimateFullSessionScreenTimes(any(), any(), any()) } returns reEstimatedSession

        // When: Repository is initialized and processes battery status
        repository = createRepository()
        repository.processBatteryStatus(liveStatus)

        // Then: Full session re-estimation should be called
        coVerify { mockFullSessionReEstimator.reEstimateFullSessionScreenTimes(cachedSession, liveStatus, any()) }
        
        // And: Session should be updated with re-estimated values
        assertEquals(reEstimatedSession, repository.currentSession.value)
    }

    @Test
    fun `test screen time UI tracking initialization`() = runTest {
        // Given: A cached session with screen times
        val cachedSession = createTestSession(
            screenOnTimeMs = 1800000, // 30 minutes
            screenOffTimeMs = 3600000, // 60 minutes
            isActive = true
        )

        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession

        // When: Repository is initialized
        repository = createRepository()

        // Then: Screen time UI should be initialized with session data
        val (onTime, offTime) = repository.incrementScreenTimeForUI()
        
        // The UI times should start from the session data
        assertTrue("Screen on time should be initialized", onTime >= cachedSession.screenOnTimeMillis)
        assertTrue("Screen off time should be initialized", offTime >= cachedSession.screenOffTimeMillis)
    }

    @Test
    fun `test screen time increment without session`() = runTest {
        // Given: No cached session
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns null

        // When: Repository is initialized and screen time is incremented
        repository = createRepository()
        val (onTime1, offTime1) = repository.incrementScreenTimeForUI()
        
        // Wait a bit and increment again
        Thread.sleep(100)
        val (onTime2, offTime2) = repository.incrementScreenTimeForUI()

        // Then: Times should increment based on current screen state
        if (mockPowerManager.isInteractive) {
            assertTrue("Screen on time should increment", onTime2 > onTime1)
            assertEquals("Screen off time should not change", offTime1, offTime2)
        } else {
            assertEquals("Screen on time should not change", onTime1, onTime2)
            assertTrue("Screen off time should increment", offTime2 > offTime1)
        }
    }

    @Test
    fun `test new session creation when no cached session`() = runTest {
        // Given: No cached session
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns null
        
        val newSession = createTestSession(isActive = true)
        every { mockSessionManager.createNewSession(any()) } returns newSession

        // When: Repository processes battery status
        repository = createRepository()
        val status = CoreBatteryStatus(
            percentage = 85,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -250000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )
        repository.processBatteryStatus(status)

        // Then: New session should be created
        verify { mockSessionManager.createNewSession(status) }
        assertEquals(newSession, repository.currentSession.value)
    }

    private fun createRepository(): DischargeSessionRepository {
        return DischargeSessionRepository(
            context = mockContext,
            currentSessionCache = mockCurrentSessionCache,
            sessionManager = mockSessionManager,
            gapEstimationCalculator = mockGapEstimationCalculator,
            fullSessionReEstimator = mockFullSessionReEstimator,
            sessionMetricsCalculator = mockSessionMetricsCalculator,
            screenStateReceiver = mockScreenStateReceiver
        )
    }

    private fun createTestSession(
        startTime: Long = System.currentTimeMillis(),
        startPercentage: Int = 100,
        currentPercentage: Int = 90,
        isActive: Boolean = true,
        screenOnTimeMs: Long = 0L,
        screenOffTimeMs: Long = 0L
    ): DischargeSessionData {
        return DischargeSessionData(
            startTimeEpochMillis = startTime,
            lastUpdateTimeEpochMillis = System.currentTimeMillis(),
            startPercentage = startPercentage,
            currentPercentage = currentPercentage,
            currentPercentageAtLastUpdate = currentPercentage,
            isActive = isActive,
            screenOnTimeMillis = screenOnTimeMs,
            screenOffTimeMillis = screenOffTimeMs,
            totalMahConsumed = 0.0,
            screenOnMahConsumed = 0.0,
            screenOffMahConsumed = 0.0,
            avgScreenOnDischargeRateMahPerHour = 250.0,
            avgScreenOffDischargeRateMahPerHour = 50.0,
            avgMixedDischargeRateMahPerHour = 120.0,
            avgPercentPerHour = 10.0,
            currentDischargeRate = 8.0
        )
    }
}
