package com.tqhit.battery.one.features.stats.discharge.domain

import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for the enhanced ScreenStateTimeTracker with gap estimation caching
 */
class EnhancedScreenTimeTrackerTest {

    private lateinit var tracker: ScreenStateTimeTracker

    @Before
    fun setUp() {
        tracker = ScreenStateTimeTracker()
    }

    @Test
    fun `test simplified gap estimation functionality`() = runTest {
        // Initialize tracker
        tracker.initialize(0L, 0L, true) // Start fresh

        // Apply gap estimation results (simplified approach)
        val sessionStartTime = System.currentTimeMillis() - 60000L // 1 minute ago
        tracker.applyGapEstimationResults(15000L, sessionStartTime) // 15s ON time from gap estimation

        // Verify that the simplified logic works
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should be the gap estimation value (15s)
        assertEquals("ON time should be from gap estimation", 15000L, onTime)
        // OFF time should be calculated as total_session_time - on_time
        // Total session time = 60s, ON time = 15s, so OFF time should be ~45s
        assertTrue("OFF time should be calculated as total - ON", offTime >= 40000L && offTime <= 50000L)
    }

    @Test
    fun `test robust timer logic with screen ON state`() = runTest {
        // Initialize and apply gap estimation for screen ON state
        tracker.initialize(0L, 0L, true)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Wait a bit and increment
        Thread.sleep(100)
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should increase from gap estimation value
        assertTrue("ON time should be greater than gap estimation value", onTime > 10000L)
        // OFF time should be calculated as total - ON time
        assertTrue("OFF time should be calculated from total", offTime >= 4000L && offTime <= 6000L)
    }

    @Test
    fun `test robust timer logic with screen OFF state`() = runTest {
        // Initialize and apply gap estimation for screen OFF state
        tracker.initialize(0L, 0L, false)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Wait a bit and increment
        Thread.sleep(100)
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should remain stable (screen is OFF), OFF time calculated from total
        assertEquals("ON time should remain from gap estimation", 10000L, onTime)
        assertTrue("OFF time should be calculated from total", offTime >= 4000L && offTime <= 6000L)
    }

    @Test
    fun `test fallback to original logic when cache invalid`() = runTest {
        // Initialize without caching gap estimation
        tracker.initialize(5000L, 3000L, true)
        
        // Should use original increment logic
        val (onTime1, offTime1) = tracker.incrementCurrentState()
        Thread.sleep(100)
        val (onTime2, offTime2) = tracker.incrementCurrentState()
        
        // Should increment normally
        assertTrue("ON time should increase", onTime2 > onTime1)
        assertEquals("OFF time should remain same", offTime1, offTime2)
    }

    @Test
    fun `test force set screen state updates timestamps`() = runTest {
        // Initialize and apply gap estimation
        tracker.initialize(0L, 0L, true)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Force change screen state
        tracker.forceSetScreenState(false)

        // Increment should now use OFF logic
        Thread.sleep(100)
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should remain stable, OFF time calculated from total
        assertEquals("ON time should remain from gap estimation", 10000L, onTime)
        assertTrue("OFF time should be calculated from total", offTime >= 4000L && offTime <= 6000L)
    }

    @Test
    fun `test reset clears gap estimation state`() = runTest {
        // Initialize and apply gap estimation
        tracker.initialize(10000L, 5000L, true)
        val sessionStartTime = System.currentTimeMillis() - 23000L // 23 seconds ago
        tracker.applyGapEstimationResults(15000L, sessionStartTime) // 15s ON time

        // Reset tracker
        tracker.reset()

        // Initialize again
        tracker.initialize(1000L, 2000L, true)

        // Should use original logic (gap estimation cleared)
        val (onTime1, offTime1) = tracker.incrementCurrentState()
        Thread.sleep(100)
        val (onTime2, offTime2) = tracker.incrementCurrentState()

        // Should increment from initialized values, not gap estimation values
        assertTrue("Should start from reset values", onTime1 >= 1000L && onTime1 < 15000L)
        assertTrue("Should increment normally", onTime2 > onTime1)
    }

    @Test
    fun `test gap estimation prevents time going backwards`() = runTest {
        // Initialize with higher values
        tracker.initialize(20000L, 15000L, true)

        // Try to apply gap estimation with lower values (should not go backwards)
        val sessionStartTime = System.currentTimeMillis() - 18000L // 18 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // Lower ON time

        val (onTime, offTime) = tracker.incrementCurrentState()

        // Should not go backwards from initialized values
        assertTrue("ON time should not go backwards", onTime >= 20000L)
        assertTrue("OFF time should not go backwards", offTime >= 15000L)
    }

    @Test
    fun `test multiple increments with gap estimation`() = runTest {
        // Initialize and apply gap estimation
        tracker.initialize(0L, 0L, true)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Multiple increments should show consistent behavior
        val results = mutableListOf<Pair<Long, Long>>()
        repeat(3) {
            Thread.sleep(50)
            results.add(tracker.incrementCurrentState())
        }

        // ON time should consistently increase (screen is ON)
        for (i in 1 until results.size) {
            assertTrue("ON time should increase", results[i].first > results[i-1].first)
            // OFF time calculated as total - ON, may vary slightly
            assertTrue("OFF time should be reasonable", results[i].second >= 0L)
        }
    }

    @Test
    fun `test screen state change during gap estimation operation`() = runTest {
        // Initialize and apply gap estimation for ON state
        tracker.initialize(0L, 0L, true)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Simulate screen state change
        tracker.handleScreenStateChange(false)

        // Now increments should use OFF logic
        Thread.sleep(100)
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should remain stable (screen is OFF), OFF time calculated from total
        assertEquals("ON time should remain from gap estimation", 10000L, onTime)
        assertTrue("OFF time should be calculated from total", offTime >= 4000L && offTime <= 6000L)
    }

    @Test
    fun `test gap estimation prevents backward time jumps`() = runTest {
        // Initialize tracker
        tracker.initialize(0L, 0L, true) // Screen ON

        // Apply gap estimation results - large values to test
        val sessionStartTime = System.currentTimeMillis() - 1500000L // 25 minutes ago
        tracker.applyGapEstimationResults(1000000L, sessionStartTime) // 1000s ON time

        // Wait and increment - should only increase ON time
        Thread.sleep(100)
        val (onTime1, offTime1) = tracker.incrementCurrentState()

        // ON time should increase from gap estimation value
        assertTrue("ON time should increase from gap estimation value", onTime1 > 1000000L)
        // OFF time calculated as total - ON
        assertTrue("OFF time should be calculated from total", offTime1 >= 400000L && offTime1 <= 600000L)

        // Change to OFF state
        tracker.handleScreenStateChange(false)

        // Wait and increment - should now use OFF logic
        Thread.sleep(100)
        val (onTime2, offTime2) = tracker.incrementCurrentState()

        // ON time should remain stable, OFF time recalculated
        assertEquals("ON time should remain stable after state change", onTime1, onTime2)
        assertTrue("OFF time should be recalculated", offTime2 >= offTime1)

        // Verify no backward jumps
        assertTrue("ON time should never go backwards", onTime2 >= onTime1)
        assertTrue("OFF time should never go backwards", offTime2 >= offTime1)
    }

    @Test
    fun `test force set screen state updates correctly`() = runTest {
        // Initialize and apply gap estimation
        tracker.initialize(0L, 0L, true)
        val sessionStartTime = System.currentTimeMillis() - 15000L // 15 seconds ago
        tracker.applyGapEstimationResults(10000L, sessionStartTime) // 10s ON time

        // Force change to OFF state
        tracker.forceSetScreenState(false)

        // Increment should now use OFF logic
        Thread.sleep(100)
        val (onTime, offTime) = tracker.incrementCurrentState()

        // ON time should remain from gap estimation, OFF time calculated from total
        assertEquals("ON time should remain from gap estimation after force set", 10000L, onTime)
        assertTrue("OFF time should be calculated from total", offTime >= 4000L && offTime <= 6000L)
    }

    @Test
    fun `test simplified approach - OFF time equals total minus ON time`() = runTest {
        // Test the simplified logic: OFF time = Total session time - ON time

        // 1. Initialize tracker
        tracker.initialize(0L, 0L, true) // Screen starts ON

        // 2. Apply gap estimation (simulating app restart)
        val sessionStartTime = System.currentTimeMillis() - 120000L // 2 minutes ago
        tracker.applyGapEstimationResults(60000L, sessionStartTime) // 60s ON time from gap estimation

        // 3. Increment and verify calculation
        val (onTime1, offTime1) = tracker.incrementCurrentState()

        // ON time should be the gap estimation value (60s)
        assertEquals("ON time should be from gap estimation", 60000L, onTime1)
        // OFF time should be total_session_time - on_time = 120s - 60s = 60s
        assertTrue("OFF time should be calculated as total - ON", offTime1 >= 55000L && offTime1 <= 65000L)

        // 4. Change state to OFF and continue tracking
        tracker.handleScreenStateChange(false)

        // 5. Wait and increment (screen now OFF)
        Thread.sleep(100)
        val (onTime2, offTime2) = tracker.incrementCurrentState()

        // ON time should remain the same (not tracking ON anymore)
        assertEquals("ON time should remain stable when screen OFF", onTime1, onTime2)
        // OFF time should still be calculated as total - ON (total increased slightly)
        assertTrue("OFF time should increase as total session time increases", offTime2 > offTime1)

        // 6. Change back to ON
        tracker.handleScreenStateChange(true)

        // 7. Wait and increment (screen now ON again)
        Thread.sleep(100)
        val (onTime3, offTime3) = tracker.incrementCurrentState()

        // ON time should start increasing again (tracking ON time)
        assertTrue("ON time should increase when screen ON", onTime3 > onTime2)
        // OFF time should be recalculated as total - new_ON_time
        assertTrue("OFF time should be recalculated", offTime3 <= offTime2) // Might decrease as ON time increases
    }

    @Test
    fun `test fixed issue - only active state updates`() = runTest {
        // Test that only the currently active state updates

        // Initialize and apply gap estimation
        tracker.initialize(0L, 0L, true) // Screen starts ON
        val sessionStartTime = System.currentTimeMillis() - 60000L // 1 minute ago
        tracker.applyGapEstimationResults(30000L, sessionStartTime) // 30s ON time

        // Screen is ON - only ON time should update
        Thread.sleep(100)
        val (onTime1, offTime1) = tracker.incrementCurrentState()

        // ON time should increase (screen is ON)
        assertTrue("ON time should increase when screen is ON", onTime1 > 30000L)
        // OFF time should be calculated as total - ON (around 30s since total is ~60s)
        assertTrue("OFF time should be calculated from total", offTime1 >= 25000L && offTime1 <= 35000L)

        // Change to OFF state
        tracker.handleScreenStateChange(false)

        // Screen is OFF - only OFF time should update (via total calculation)
        Thread.sleep(100)
        val (onTime2, offTime2) = tracker.incrementCurrentState()

        // ON time should remain stable (not tracking ON anymore)
        assertEquals("ON time should remain stable when screen is OFF", onTime1, onTime2)
        // OFF time should be recalculated from total (may increase as total session time increases)
        assertTrue("OFF time should be recalculated when screen is OFF", offTime2 >= offTime1)
    }

    @Test
    fun `test gap estimation fallbacks for short sessions`() = runTest {
        // Test fallback mechanisms for edge cases

        // Test 1: Very short session (< 30 seconds)
        tracker.initialize(0L, 0L, true)
        val shortSessionStart = System.currentTimeMillis() - 20000L // 20 seconds ago
        tracker.applyGapEstimationResults(0L, shortSessionStart) // 0 ON time from gap estimation

        val (onTime1, offTime1) = tracker.incrementCurrentState()

        // Should use 50% fallback for very short sessions
        assertTrue("Short session should use proportional fallback", onTime1 >= 8000L && onTime1 <= 12000L) // ~10s (50% of 20s)

        // Test 2: Screen ON time was 0 for longer session
        tracker.reset()
        tracker.initialize(0L, 0L, true)
        val longerSessionStart = System.currentTimeMillis() - 120000L // 2 minutes ago
        tracker.applyGapEstimationResults(0L, longerSessionStart) // 0 ON time from gap estimation

        val (onTime2, offTime2) = tracker.incrementCurrentState()

        // Should use 10% fallback for longer sessions with 0 ON time
        assertTrue("Longer session with 0 ON time should use 10% fallback", onTime2 >= 10000L && onTime2 <= 15000L) // ~12s (10% of 120s)

        // Test 3: Screen ON time exceeds total session time
        tracker.reset()
        tracker.initialize(0L, 0L, true)
        val sessionStart = System.currentTimeMillis() - 60000L // 1 minute ago
        tracker.applyGapEstimationResults(120000L, sessionStart) // 2 minutes ON time (impossible!)

        val (onTime3, offTime3) = tracker.incrementCurrentState()

        // Should cap at 80% of total session time
        assertTrue("Excessive ON time should be capped", onTime3 <= 50000L) // Max 80% of 60s = 48s
    }
}
