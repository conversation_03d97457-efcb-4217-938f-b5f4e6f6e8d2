package com.tqhit.battery.one.features.stats.discharge.integration

import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import com.tqhit.battery.one.features.stats.discharge.domain.AppState
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService
import com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult
import com.tqhit.battery.one.features.stats.discharge.domain.CorrectionType
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Integration tests for screen time UI update flow
 */
@ExperimentalCoroutinesApi
class ScreenTimeUiUpdateIntegrationTest {
    
    private lateinit var appLifecycleManager: AppLifecycleManager
    private lateinit var validationService: ScreenTimeValidationService
    
    @Before
    fun setUp() {
        // Mock ProcessLifecycleOwner to prevent actual lifecycle registration
        mockkStatic(ProcessLifecycleOwner::class)
        val mockProcessLifecycleOwner = mockk<ProcessLifecycleOwner>(relaxed = true)
        every { ProcessLifecycleOwner.get() } returns mockProcessLifecycleOwner

        appLifecycleManager = AppLifecycleManager()
        validationService = ScreenTimeValidationService(appLifecycleManager)
    }
    
    @Test
    fun `complete flow - app minimized then resumed with screen OFF time gap`() = runTest {
        // Arrange - Simulate app in foreground with active discharge fragment
        appLifecycleManager.setDischargeFragmentActive(true)
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Initial screen times with a gap
        val screenOnTime = 30000L // 30 seconds
        val screenOffTime = 40000L // 40 seconds (total 70 seconds)
        val sessionDuration = 120000L // 120 seconds (50 second gap)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act 1 - Validate while fragment is active (should not trigger correction)
        assertFalse(validationService.shouldTriggerUiUpdate())
        val result1 = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert 1 - No correction should be applied when fragment is active
        assertTrue(result1 is ValidationResult.Valid)
        
        // Act 2 - Simulate app going to background (user minimizes app)
        val mockOwner = mockk<androidx.lifecycle.LifecycleOwner>()
        appLifecycleManager.onStop(mockOwner)
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
        
        // Act 3 - Simulate app returning to foreground (user resumes app)
        appLifecycleManager.onStart(mockOwner)
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        
        // Act 4 - Fragment becomes inactive (user switches to different tab)
        appLifecycleManager.setDischargeFragmentActive(false)
        
        // Assert 4 - Should trigger UI update now
        assertTrue(validationService.shouldTriggerUiUpdate())
        
        // Act 5 - Validate screen times (should trigger correction)
        val result2 = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert 5 - Correction should be applied
        assertTrue(result2 is ValidationResult.Corrected)
        val correctedResult = result2 as ValidationResult.Corrected
        assertEquals(screenOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(90000L, correctedResult.correctedScreenOffTime) // 120s - 30s = 90s
        assertEquals(CorrectionType.OFF_TIME_ADJUSTED, correctedResult.correctionType)
    }
    
    @Test
    fun `validation during discharge start ensures proper screen time calculation`() = runTest {
        // Arrange - Simulate discharge session starting
        val sessionDuration = 60000L // 60 seconds
        val screenOnTime = 45000L // 45 seconds
        val screenOffTime = 10000L // 10 seconds (total 55 seconds, 5 second deficit)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act - Validate when device begins discharging
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession,
            shouldForceCorrection = true // Force validation at discharge start
        )
        
        // Assert - Should correct the screen OFF time to match session duration
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(screenOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(15000L, correctedResult.correctedScreenOffTime) // 60s - 45s = 15s
        assertEquals(CorrectionType.OFF_TIME_ADJUSTED, correctedResult.correctionType)
    }
    
    @Test
    fun `proportional scaling applied for significant time excess`() = runTest {
        // Arrange - Screen times significantly exceed session duration
        val sessionDuration = 100000L // 100 seconds
        val screenOnTime = 80000L // 80 seconds
        val screenOffTime = 60000L // 60 seconds (total 140 seconds, 40% excess)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession,
            shouldForceCorrection = true
        )
        
        // Assert - Should apply proportional scaling
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(CorrectionType.PROPORTIONAL_SCALED, correctedResult.correctionType)
        
        // Check scaling factor (100/140 ≈ 0.714)
        val expectedScaleFactor = sessionDuration.toDouble() / (screenOnTime + screenOffTime).toDouble()
        val expectedOnTime = (screenOnTime * expectedScaleFactor).toLong()
        val expectedOffTime = (screenOffTime * expectedScaleFactor).toLong()
        
        assertEquals(expectedOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(expectedOffTime, correctedResult.correctedScreenOffTime)
    }
    
    @Test
    fun `ui update triggered when app resumes regardless of fragment state`() = runTest {
        // Arrange - App in background, fragment active
        val mockOwner = mockk<androidx.lifecycle.LifecycleOwner>()
        appLifecycleManager.onStop(mockOwner)
        appLifecycleManager.setDischargeFragmentActive(true)
        
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        assertFalse(validationService.shouldTriggerUiUpdate())
        
        // Act - App comes to foreground
        appLifecycleManager.onStart(mockOwner)
        
        // Assert - Should trigger UI update even though fragment is active (recent resume)
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        assertTrue(validationService.shouldTriggerUiUpdate())
    }
    
    @Test
    fun `no ui update when fragment active and not recently resumed`() = runTest {
        // Arrange - App in foreground, fragment active, not recently resumed
        appLifecycleManager.setDischargeFragmentActive(true)
        
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Act & Assert - Should not trigger UI update
        assertFalse(validationService.shouldTriggerUiUpdate())
    }
    
    @Test
    fun `validation ensures screen time sum does not exceed total discharge time`() = runTest {
        // Arrange - Test the core requirement: (screen ON + screen OFF) ≤ total discharge time
        val sessionDuration = 90000L // 90 seconds
        val screenOnTime = 60000L // 60 seconds
        val screenOffTime = 50000L // 50 seconds (total 110 seconds > 90 seconds)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession,
            shouldForceCorrection = true
        )
        
        // Assert - Should correct to ensure sum ≤ session duration
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        
        val correctedSum = correctedResult.correctedScreenOnTime + correctedResult.correctedScreenOffTime
        assertTrue("Corrected sum ($correctedSum) should be ≤ session duration ($sessionDuration)", 
                  correctedSum <= sessionDuration)
    }
    
    private fun createMockSession(isActive: Boolean, durationMs: Long): DischargeSessionData {
        return mockk<DischargeSessionData>().apply {
            every { <EMAIL> } returns isActive
            every { <EMAIL> } returns durationMs
        }
    }
}
