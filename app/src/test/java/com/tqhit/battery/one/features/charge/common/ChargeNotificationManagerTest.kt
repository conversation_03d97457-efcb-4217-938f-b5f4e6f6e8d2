package com.tqhit.battery.one.features.charge.common

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.charge.data.model.NewChargeStatus
import com.tqhit.battery.one.repository.AppRepository
import io.mockk.*
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.lang.reflect.Method

@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [28])
class ChargeNotificationManagerTest {

    // Mocks
    private lateinit var context: Context
    private lateinit var appRepository: AppRepository
    private lateinit var notificationManager: ChargeNotificationManager
    
    @Before
    fun setup() {
        // Configure Shadow Log to avoid Log.d issues
        ShadowLog.stream = System.out
        
        // Initialize mocks
        context = mockk(relaxed = true)
        appRepository = mockk(relaxed = true)
        
        // Mock NotificationManager
        val androidNotificationManager = mockk<android.app.NotificationManager>(relaxed = true)
        every { context.getSystemService(Context.NOTIFICATION_SERVICE) } returns androidNotificationManager
        
        // Mock string resources
        every { context.getString(R.string.charge) } returns "Charging"
        
        // Create the notification manager
        notificationManager = ChargeNotificationManager(context, appRepository)
    }
    
    @Test
    fun test_calculatePower_positiveCurrentAndVoltage_calculatesCorrectly() {
        // Test data
        val currentMicroAmperes = 1500000L // 1.5A
        val voltageMillivolts = 5000 // 5V
        
        // Calculate power manually using the same formula as in ChargeNotificationManager
        val power = (currentMicroAmperes / 1000.0f) * (voltageMillivolts / 1000.0f) / 1000.0f
        val expectedPowerText = String.format("%.1f", power)
        
        // Verify the calculation is correct
        assertEquals(7.5f, power, 0.01f)
        assertEquals("7.5", expectedPowerText)
    }
    
    private fun assertEquals(expected: Float, actual: Float, delta: Float) {
        assertTrue("Expected: $expected, Actual: $actual, Delta: $delta", 
            Math.abs(expected - actual) <= delta)
    }
    
    private fun assertEquals(expected: String, actual: String) {
        assertTrue("Expected: $expected, Actual: $actual", expected == actual)
    }
} 