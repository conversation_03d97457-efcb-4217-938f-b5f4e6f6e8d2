package com.tqhit.battery.one.features.stats.charge

import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for StatsChargeSession data class.
 * Tests session creation, duration calculation, and percentage charged logic.
 */
class StatsChargeSessionTest {
    
    @Test
    fun `test active session creation`() {
        // Given
        val startTime = System.currentTimeMillis()
        val startPercentage = 50
        
        // When
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = null,
            startPercentage = startPercentage,
            endPercentage = null,
            isActive = true
        )
        
        // Then
        assertEquals(startTime, session.startTimeEpochMillis)
        assertNull(session.endTimeEpochMillis)
        assertEquals(startPercentage, session.startPercentage)
        assertNull(session.endPercentage)
        assertTrue(session.isActive)
    }
    
    @Test
    fun `test completed session creation`() {
        // Given
        val startTime = System.currentTimeMillis() - 3600000 // 1 hour ago
        val endTime = System.currentTimeMillis()
        val startPercentage = 50
        val endPercentage = 85
        
        // When
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = endTime,
            startPercentage = startPercentage,
            endPercentage = endPercentage,
            isActive = false
        )
        
        // Then
        assertEquals(startTime, session.startTimeEpochMillis)
        assertEquals(endTime, session.endTimeEpochMillis)
        assertEquals(startPercentage, session.startPercentage)
        assertEquals(endPercentage, session.endPercentage)
        assertFalse(session.isActive)
    }
    
    @Test
    fun `test duration calculation for active session`() {
        // Given
        val startTime = System.currentTimeMillis() - 1800000 // 30 minutes ago
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val duration = session.durationMillis
        
        // Then - should be approximately 30 minutes (allow some tolerance for test execution time)
        assertTrue("Duration should be around 30 minutes", duration >= 1800000 - 1000 && duration <= 1800000 + 1000)
    }
    
    @Test
    fun `test duration calculation for completed session`() {
        // Given
        val startTime = 1000000L
        val endTime = 2800000L // 30 minutes later
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = endTime,
            startPercentage = 50,
            endPercentage = 85,
            isActive = false
        )
        
        // When
        val duration = session.durationMillis
        
        // Then
        assertEquals(1800000L, duration) // Exactly 30 minutes
    }
    
    @Test
    fun `test percentage charged for completed session`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 30,
            endPercentage = 75,
            isActive = false
        )
        
        // When
        val percentageCharged = session.percentageCharged
        
        // Then
        assertEquals(45, percentageCharged)
    }
    
    @Test
    fun `test percentage charged for active session returns zero`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 30,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val percentageCharged = session.percentageCharged
        
        // Then
        assertEquals(0, percentageCharged)
    }
    
    @Test
    fun `test getPercentageCharged with current percentage for active session`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 40,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val percentageCharged = session.getPercentageCharged(65)
        
        // Then
        assertEquals(25, percentageCharged)
    }
    
    @Test
    fun `test getPercentageCharged with current percentage for completed session`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 20,
            endPercentage = 80,
            isActive = false
        )
        
        // When
        val percentageCharged = session.getPercentageCharged(90) // Should ignore current percentage
        
        // Then
        assertEquals(60, percentageCharged) // Uses endPercentage - startPercentage
    }
    
    @Test
    fun `test percentage charged handles negative values`() {
        // Given - edge case where end percentage is lower than start (shouldn't happen in practice)
        val session = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 80,
            endPercentage = 70,
            isActive = false
        )
        
        // When
        val percentageCharged = session.percentageCharged
        
        // Then
        assertEquals(0, percentageCharged) // Should return 0, not negative
    }
    
    @Test
    fun `test getPercentageCharged handles negative values for active session`() {
        // Given - edge case where current percentage is lower than start
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 80,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val percentageCharged = session.getPercentageCharged(70)
        
        // Then
        assertEquals(0, percentageCharged) // Should return 0, not negative
    }
    
    @Test
    fun `test createNew factory method`() {
        // Given
        val startPercentage = 45
        val beforeTime = System.currentTimeMillis()
        
        // When
        val session = StatsChargeSession.createNew(startPercentage)
        val afterTime = System.currentTimeMillis()
        
        // Then
        assertTrue("Start time should be recent", session.startTimeEpochMillis >= beforeTime && session.startTimeEpochMillis <= afterTime)
        assertNull(session.endTimeEpochMillis)
        assertEquals(startPercentage, session.startPercentage)
        assertNull(session.endPercentage)
        assertTrue(session.isActive)
    }
    
    @Test
    fun `test endSession factory method`() {
        // Given
        val activeSession = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis() - 1000000,
            endTimeEpochMillis = null,
            startPercentage = 30,
            endPercentage = null,
            isActive = true
        )
        val endPercentage = 85
        val beforeTime = System.currentTimeMillis()
        
        // When
        val endedSession = StatsChargeSession.endSession(activeSession, endPercentage)
        val afterTime = System.currentTimeMillis()
        
        // Then
        assertEquals(activeSession.startTimeEpochMillis, endedSession.startTimeEpochMillis)
        assertTrue("End time should be recent", endedSession.endTimeEpochMillis!! >= beforeTime && endedSession.endTimeEpochMillis!! <= afterTime)
        assertEquals(activeSession.startPercentage, endedSession.startPercentage)
        assertEquals(endPercentage, endedSession.endPercentage)
        assertFalse(endedSession.isActive)
    }
    
    @Test
    fun `test data class equality`() {
        // Given
        val session1 = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 50,
            endPercentage = 80,
            isActive = false
        )
        
        val session2 = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 50,
            endPercentage = 80,
            isActive = false
        )
        
        // Then
        assertEquals(session1, session2)
        assertEquals(session1.hashCode(), session2.hashCode())
    }
    
    @Test
    fun `test data class inequality`() {
        // Given
        val session1 = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 50,
            endPercentage = 80,
            isActive = false
        )
        
        val session2 = StatsChargeSession(
            startTimeEpochMillis = 1000000L,
            endTimeEpochMillis = 2000000L,
            startPercentage = 60, // Different start percentage
            endPercentage = 80,
            isActive = false
        )
        
        // Then
        assertNotEquals(session1, session2)
    }
}
