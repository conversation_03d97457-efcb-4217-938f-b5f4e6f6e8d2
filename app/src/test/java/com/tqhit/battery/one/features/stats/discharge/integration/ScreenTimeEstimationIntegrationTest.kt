package com.tqhit.battery.one.features.stats.discharge.integration

import android.content.Context
import android.os.PowerManager
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.*
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Integration test for screen time estimation during app restart scenarios
 */
@OptIn(ExperimentalCoroutinesApi::class)
class ScreenTimeEstimationIntegrationTest {

    private lateinit var repository: DischargeSessionRepository
    private lateinit var mockContext: Context
    private lateinit var mockPowerManager: PowerManager
    private lateinit var mockCurrentSessionCache: CurrentSessionCache
    private lateinit var mockSessionManager: SessionManager
    private lateinit var mockGapEstimationCalculator: GapEstimationCalculator
    private lateinit var mockFullSessionReEstimator: FullSessionReEstimator
    private lateinit var mockSessionMetricsCalculator: SessionMetricsCalculator
    private lateinit var mockScreenStateReceiver: ScreenStateReceiver

    @Before
    fun setUp() {
        mockContext = mockk()
        mockPowerManager = mockk()
        mockCurrentSessionCache = mockk()
        mockSessionManager = mockk()
        mockGapEstimationCalculator = mockk()
        mockFullSessionReEstimator = mockk()
        mockSessionMetricsCalculator = mockk()
        mockScreenStateReceiver = mockk()

        // Mock context.getSystemService to return our mock PowerManager
        every { mockContext.getSystemService(Context.POWER_SERVICE) } returns mockPowerManager

        // Mock screen state receiver
        every { mockScreenStateReceiver.register() } just Runs
        every { mockScreenStateReceiver.unregister() } just Runs
        every { mockScreenStateReceiver.forceCheckScreenState() } just Runs
        every { mockScreenStateReceiver.screenStateFlow } returns MutableStateFlow(
            com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent(true)
        )

        // Mock session cache
        coEvery { mockCurrentSessionCache.saveCurrentSession(any()) } just Runs
        coEvery { mockCurrentSessionCache.clearCurrentSession() } just Runs
    }

    @Test
    fun `test screen time estimation consistency after app restart with screen state change`() = runTest {
        // Scenario: App was closed with screen ON, reopened with screen OFF
        // This tests the most common case where screen state changes during app closure
        
        // Given: A cached session that was saved when screen was ON
        val sessionStartTime = System.currentTimeMillis() - 7200000L // 2 hours ago
        val cachedSession = createTestSession(
            startTime = sessionStartTime,
            startPercentage = 90,
            currentPercentage = 85, // 5% drop when app was closed
            screenOnTimeMs = 1800000L, // 30 minutes screen on
            screenOffTimeMs = 1800000L  // 30 minutes screen off
        )

        // App restart: Screen is now OFF, battery dropped to 80%
        every { mockPowerManager.isInteractive } returns false // Screen is OFF now
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession

        // Mock the full session re-estimation
        val reEstimatedSession = cachedSession.copy(
            currentPercentage = 80, // 10% total drop
            screenOnTimeMillis = 2400000L, // 40 minutes estimated (10 min more)
            screenOffTimeMillis = 4800000L, // 80 minutes estimated (50 min more)
            totalMahConsumed = 300.0
        )
        coEvery { mockFullSessionReEstimator.reEstimateFullSessionScreenTimes(any(), any(), any()) } returns reEstimatedSession

        // When: Repository is initialized and processes battery status
        repository = createRepository()
        
        val liveStatus = CoreBatteryStatus(
            percentage = 80,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -250000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )
        repository.processBatteryStatus(liveStatus)

        // Then: Session should be re-estimated
        coVerify { mockFullSessionReEstimator.reEstimateFullSessionScreenTimes(cachedSession, liveStatus, any()) }
        assertEquals("Session should be updated with re-estimated values", reEstimatedSession, repository.currentSession.value)

        // And: UI screen time should be consistent with re-estimated session
        val (uiOnTime, uiOffTime) = repository.incrementScreenTimeForUI()
        
        // UI times should start from re-estimated values and be close to session data
        val onTimeDiff = kotlin.math.abs(uiOnTime - reEstimatedSession.screenOnTimeMillis)
        val offTimeDiff = kotlin.math.abs(uiOffTime - reEstimatedSession.screenOffTimeMillis)
        
        assertTrue("UI ON time should be close to session data (diff: ${onTimeDiff}ms)", onTimeDiff < 5000L)
        assertTrue("UI OFF time should be close to session data (diff: ${offTimeDiff}ms)", offTimeDiff < 5000L)
    }

    @Test
    fun `test screen time tracking continues correctly after restart`() = runTest {
        // Scenario: Verify that screen time continues to increment correctly after restart
        
        val cachedSession = createTestSession(
            startTime = System.currentTimeMillis() - 3600000L, // 1 hour ago
            screenOnTimeMs = 1800000L, // 30 minutes
            screenOffTimeMs = 1800000L  // 30 minutes
        )

        // Screen is ON during restart
        every { mockPowerManager.isInteractive } returns true
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns cachedSession

        // No re-estimation needed (small time gap)
        coEvery { mockFullSessionReEstimator.reEstimateFullSessionScreenTimes(any(), any(), any()) } returns cachedSession

        // When: Repository is initialized
        repository = createRepository()

        // Get initial UI times
        val (initialOnTime, initialOffTime) = repository.incrementScreenTimeForUI()
        
        // Simulate time passing (screen ON)
        Thread.sleep(100) // Small delay to simulate time passage
        val (laterOnTime, laterOffTime) = repository.incrementScreenTimeForUI()

        // Then: Screen ON time should increment, OFF time should stay the same
        assertTrue("Screen ON time should increment", laterOnTime > initialOnTime)
        assertEquals("Screen OFF time should not change", initialOffTime, laterOffTime)
        
        // And: Times should be based on cached session data
        assertTrue("ON time should be based on cached session", initialOnTime >= cachedSession.screenOnTimeMillis)
        assertTrue("OFF time should be based on cached session", initialOffTime >= cachedSession.screenOffTimeMillis)
    }

    @Test
    fun `test screen time estimation with no cached session`() = runTest {
        // Scenario: Fresh start with no cached session
        
        // Given: No cached session
        every { mockPowerManager.isInteractive } returns true
        coEvery { mockCurrentSessionCache.getCurrentSession() } returns null

        val newSession = createTestSession(isActive = true)
        every { mockSessionManager.createNewSession(any()) } returns newSession

        // When: Repository is initialized and processes battery status
        repository = createRepository()
        
        val status = CoreBatteryStatus(
            percentage = 85,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -200000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )
        repository.processBatteryStatus(status)

        // Then: New session should be created
        verify { mockSessionManager.createNewSession(status) }
        
        // And: UI times should start from zero
        val (onTime, offTime) = repository.incrementScreenTimeForUI()
        assertTrue("Initial times should be small", onTime < 5000L && offTime < 5000L)
    }

    private fun createRepository(): DischargeSessionRepository {
        return DischargeSessionRepository(
            context = mockContext,
            currentSessionCache = mockCurrentSessionCache,
            sessionManager = mockSessionManager,
            gapEstimationCalculator = mockGapEstimationCalculator,
            fullSessionReEstimator = mockFullSessionReEstimator,
            sessionMetricsCalculator = mockSessionMetricsCalculator,
            screenStateReceiver = mockScreenStateReceiver
        )
    }

    private fun createTestSession(
        startTime: Long = System.currentTimeMillis(),
        startPercentage: Int = 100,
        currentPercentage: Int = 90,
        isActive: Boolean = true,
        screenOnTimeMs: Long = 0L,
        screenOffTimeMs: Long = 0L
    ): DischargeSessionData {
        return DischargeSessionData(
            startTimeEpochMillis = startTime,
            lastUpdateTimeEpochMillis = System.currentTimeMillis(),
            startPercentage = startPercentage,
            currentPercentage = currentPercentage,
            currentPercentageAtLastUpdate = currentPercentage,
            isActive = isActive,
            screenOnTimeMillis = screenOnTimeMs,
            screenOffTimeMillis = screenOffTimeMs,
            totalMahConsumed = 0.0,
            screenOnMahConsumed = 0.0,
            screenOffMahConsumed = 0.0,
            avgScreenOnDischargeRateMahPerHour = 250.0,
            avgScreenOffDischargeRateMahPerHour = 50.0,
            avgMixedDischargeRateMahPerHour = 120.0,
            avgPercentPerHour = 10.0,
            currentDischargeRate = 8.0
        )
    }
}
