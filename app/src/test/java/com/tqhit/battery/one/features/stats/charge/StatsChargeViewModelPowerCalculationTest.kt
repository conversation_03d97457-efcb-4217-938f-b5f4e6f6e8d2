package com.tqhit.battery.one.features.stats.charge

import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
import com.tqhit.battery.one.repository.AppRepository
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import kotlin.math.abs

/**
 * Unit tests for power calculation functionality in StatsChargeViewModel.
 */
class StatsChargeViewModelPowerCalculationTest {

    private lateinit var viewModel: StatsChargeViewModel
    private lateinit var mockStatsChargeRepository: StatsChargeRepository
    private lateinit var mockCalculateSimpleChargeEstimateUseCase: CalculateSimpleChargeEstimateUseCase
    private lateinit var mockAppRepository: AppRepository

    @Before
    fun setup() {
        mockStatsChargeRepository = mockk()
        mockCalculateSimpleChargeEstimateUseCase = mockk()
        mockAppRepository = mockk()

        // Setup default mocks
        every { mockStatsChargeRepository.statsChargeStatusFlow } returns flowOf(createTestStatus())
        every { mockStatsChargeRepository.activeChargeSessionFlow } returns flowOf(null)
        coEvery { mockAppRepository.chargeAlarmPercentFlow } returns flowOf(80)
        coEvery { mockAppRepository.getBatteryCapacity() } returns 1000

        viewModel = StatsChargeViewModel(
            mockStatsChargeRepository,
            mockCalculateSimpleChargeEstimateUseCase,
            mockAppRepository
        )
    }

    @Test
    fun `test calculatePower with typical charging values`() {
        // Given
        val voltageMillivolts = 4200 // 4.2V
        val currentMicroAmperes = 1500000L // 1.5A

        // When
        val powerWatts = viewModel.calculatePower(voltageMillivolts, currentMicroAmperes)

        // Then
        val expectedPower = 6.3 // 4.2V * 1.5A = 6.3W
        assertEquals(expectedPower, powerWatts, 0.01)
    }

    @Test
    fun `test calculatePower with low power values`() {
        // Given
        val voltageMillivolts = 3700 // 3.7V
        val currentMicroAmperes = 500000L // 0.5A

        // When
        val powerWatts = viewModel.calculatePower(voltageMillivolts, currentMicroAmperes)

        // Then
        val expectedPower = 1.85 // 3.7V * 0.5A = 1.85W
        assertEquals(expectedPower, powerWatts, 0.01)
    }

    @Test
    fun `test calculatePower with zero current`() {
        // Given
        val voltageMillivolts = 4000 // 4.0V
        val currentMicroAmperes = 0L // 0A

        // When
        val powerWatts = viewModel.calculatePower(voltageMillivolts, currentMicroAmperes)

        // Then
        assertEquals(0.0, powerWatts, 0.01)
    }

    @Test
    fun `test calculatePower with negative current (discharging)`() {
        // Given
        val voltageMillivolts = 3800 // 3.8V
        val currentMicroAmperes = -800000L // -0.8A (discharging)

        // When
        val powerWatts = viewModel.calculatePower(voltageMillivolts, currentMicroAmperes)

        // Then
        val expectedPower = -3.04 // 3.8V * -0.8A = -3.04W
        assertEquals(expectedPower, powerWatts, 0.01)
    }

    @Test
    fun `test formatPower with watts`() {
        // Given
        val powerWatts = 6.3

        // When
        val formatted = viewModel.formatPower(powerWatts)

        // Then
        assertEquals("6.3W", formatted)
    }

    @Test
    fun `test formatPower with milliwatts`() {
        // Given
        val powerWatts = 0.5 // 500mW

        // When
        val formatted = viewModel.formatPower(powerWatts)

        // Then
        assertEquals("500mW", formatted)
    }

    @Test
    fun `test formatPower with very small power`() {
        // Given
        val powerWatts = 0.0005 // 0.5mW

        // When
        val formatted = viewModel.formatPower(powerWatts)

        // Then
        assertEquals("1mW", formatted) // Rounded to nearest mW
    }

    @Test
    fun `test formatPower with zero power`() {
        // Given
        val powerWatts = 0.0

        // When
        val formatted = viewModel.formatPower(powerWatts)

        // Then
        assertEquals("0W", formatted)
    }

    @Test
    fun `test formatPower with negative power`() {
        // Given
        val powerWatts = -2.5

        // When
        val formatted = viewModel.formatPower(powerWatts)

        // Then
        assertEquals("2.5W", formatted) // Should show absolute value
    }

    private fun createTestStatus(): StatsChargeStatus {
        return StatsChargeStatus(
            percentage = 50,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f
        )
    }
}
