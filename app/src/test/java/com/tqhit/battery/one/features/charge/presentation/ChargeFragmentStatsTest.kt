package com.tqhit.battery.one.features.charge.presentation

import android.os.BatteryManager
import com.tqhit.battery.one.features.charge.data.model.NewChargeStatus
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

/**
 * Tests for the stats displayed in the Charge Fragment
 * Focusing on consistent power, amperage, voltage calculations
 */
@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [28])
class ChargeFragmentStatsTest {

    @Before
    fun setup() {
        // Configure Shadow Log to avoid Log.d issues
        ShadowLog.stream = System.out
    }

    @Test
    fun test_powerCalculation_isConsistent() {
        // Create a charge status object with known values
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = BatteryManager.BATTERY_PLUGGED_AC,
            currentMicroAmperes = 1500000L, // 1.5A
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Calculate power directly using the same formula as in the ViewModel
        val powerWatts = (status.currentMicroAmperes / 1000.0f) * (status.voltageMillivolts / 1000.0f) / 1000.0f
        val expectedPowerText = String.format("%.2f W", powerWatts)
        
        // The expected power should be: (1500000 / 1000) * (5000 / 1000) / 1000 = 7.5W
        assertEquals("7.50 W", expectedPowerText)
    }
    
    @Test
    fun test_powerCalculation_withSmallCurrentValues() {
        // Create a charge status object with small current (that would be scaled by 1000)
        val rawCurrentValue = 500L // 500mA, would be scaled to 500000 μA
        val scaledCurrentValue = rawCurrentValue * 1000 // 500000 μA
        
        val statusWithScaledCurrent = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = BatteryManager.BATTERY_PLUGGED_AC,
            currentMicroAmperes = scaledCurrentValue, // 500mA scaled to μA
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Calculate power with the scaled current
        val powerWatts = (statusWithScaledCurrent.currentMicroAmperes / 1000.0f) * 
                (statusWithScaledCurrent.voltageMillivolts / 1000.0f) / 1000.0f
        val expectedPowerText = String.format("%.2f W", powerWatts)
        
        // The expected power should be: (500000 / 1000) * (5000 / 1000) / 1000 = 2.5W
        assertEquals("2.50 W", expectedPowerText)
    }
    
    @Test
    fun test_amperageText_formatting() {
        // Test with positive current
        val currentMicroAmperes = 2500000L // 2.5A
        val formattedAmperage = "${currentMicroAmperes / 1000.0f} mA"
        assertEquals("2500.0 mA", formattedAmperage)
        
        // Test with negative current
        val negativeCurrentMicroAmperes = -1200000L // -1.2A
        val formattedNegativeAmperage = "${negativeCurrentMicroAmperes / 1000.0f} mA"
        assertEquals("-1200.0 mA", formattedNegativeAmperage)
        
        // Test with very small current
        val smallCurrentMicroAmperes = 500L // 0.5mA
        val formattedSmallAmperage = "${smallCurrentMicroAmperes / 1000.0f} mA"
        assertEquals("0.5 mA", formattedSmallAmperage)
        
        // Test with very small current after scaling (500 * 1000 = 500000)
        val scaledSmallCurrentMicroAmperes = 500L * 1000 // 500mA scaled to μA
        val formattedScaledAmperage = "${scaledSmallCurrentMicroAmperes / 1000.0f} mA"
        assertEquals("500.0 mA", formattedScaledAmperage)
    }
    
    @Test
    fun test_voltageText_formatting() {
        // Test with standard voltage
        val voltageMillivolts = 4200 // 4.2V
        val formattedVoltage = "${voltageMillivolts / 1000.0f} V"
        assertEquals("4.2 V", formattedVoltage)
        
        // Test with high voltage
        val highVoltageMillivolts = 12000 // 12V
        val formattedHighVoltage = "${highVoltageMillivolts / 1000.0f} V"
        assertEquals("12.0 V", formattedHighVoltage)
    }
    
    @Test
    fun test_endToEnd_powerCalculationWithScaledValues() {
        // This test simulates what happens in the real app:
        // 1. Battery manager returns a small current (e.g., 500)
        // 2. AndroidBatteryStatsDataSource scales it by 1000 (e.g., 500 -> 500000)
        // 3. The ViewModel calculates power using this scaled value
        
        // Simulate raw value from BatteryManager
        val rawCurrentValue = 500L // 500mA
        
        // Simulate scaling in AndroidBatteryStatsDataSource
        val scaledCurrentValue = rawCurrentValue * 1000 // 500000 μA
        
        // Create charge status with scaled current
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = BatteryManager.BATTERY_PLUGGED_AC,
            currentMicroAmperes = scaledCurrentValue,
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Calculate power as it would be in the ViewModel
        val powerWatts = (status.currentMicroAmperes / 1000.0f) * (status.voltageMillivolts / 1000.0f) / 1000.0f
        val powerText = String.format("%.2f W", powerWatts)
        
        // The expected power should be: (500000 / 1000) * (5000 / 1000) / 1000 = 2.5W
        assertEquals("2.50 W", powerText)
        
        // Also verify the amperage text
        val amperageText = "${status.currentMicroAmperes / 1000.0f} mA"
        assertEquals("500.0 mA", amperageText)
    }
} 