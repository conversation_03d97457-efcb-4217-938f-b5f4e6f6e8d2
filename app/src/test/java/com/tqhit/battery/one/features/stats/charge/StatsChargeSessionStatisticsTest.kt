package com.tqhit.battery.one.features.stats.charge

import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * Unit tests for session statistics functionality in StatsChargeSession.
 */
class StatsChargeSessionStatisticsTest {

    @Test
    fun `test session with totalChargePercentage and totalChargeMah`() {
        // Given
        val startTime = System.currentTimeMillis()
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = null,
            startPercentage = 30,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 25,
            totalChargeMah = 250.0
        )

        // Then
        assertEquals(25, session.totalChargePercentage)
        assertEquals(250.0, session.totalChargeMah, 0.01)
        assertEquals(30, session.startPercentage)
        assertEquals(true, session.isActive)
    }

    @Test
    fun `test session creation with default statistics`() {
        // Given
        val startPercentage = 40

        // When
        val session = StatsChargeSession.createNew(startPercentage)

        // Then
        assertEquals(startPercentage, session.startPercentage)
        assertEquals(0, session.totalChargePercentage)
        assertEquals(0.0, session.totalChargeMah, 0.01)
        assertEquals(true, session.isActive)
        assertEquals(null, session.endTimeEpochMillis)
        assertEquals(null, session.endPercentage)
    }

    @Test
    fun `test session ending preserves statistics`() {
        // Given
        val startTime = System.currentTimeMillis() - 60000 // 1 minute ago
        val activeSession = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = null,
            startPercentage = 20,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 30,
            totalChargeMah = 300.0
        )
        val endPercentage = 50

        // When
        val endedSession = StatsChargeSession.endSession(activeSession, endPercentage)

        // Then
        assertEquals(false, endedSession.isActive)
        assertEquals(endPercentage, endedSession.endPercentage)
        assertEquals(30, endedSession.totalChargePercentage) // Preserved
        assertEquals(300.0, endedSession.totalChargeMah, 0.01) // Preserved
        assertEquals(20, endedSession.startPercentage) // Preserved
        assert(endedSession.endTimeEpochMillis != null)
    }

    @Test
    fun `test session copy with updated statistics`() {
        // Given
        val originalSession = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 10,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 0,
            totalChargeMah = 0.0
        )

        // When
        val updatedSession = originalSession.copy(
            totalChargePercentage = 15,
            totalChargeMah = 150.0
        )

        // Then
        assertEquals(15, updatedSession.totalChargePercentage)
        assertEquals(150.0, updatedSession.totalChargeMah, 0.01)
        assertEquals(10, updatedSession.startPercentage) // Unchanged
        assertEquals(true, updatedSession.isActive) // Unchanged
    }

    @Test
    fun `test session duration calculation with statistics`() {
        // Given
        val startTime = System.currentTimeMillis() - 120000 // 2 minutes ago
        val session = StatsChargeSession(
            startTimeEpochMillis = startTime,
            endTimeEpochMillis = null,
            startPercentage = 60,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 10,
            totalChargeMah = 100.0
        )

        // When
        val duration = session.durationMillis

        // Then
        assert(duration >= 120000) // At least 2 minutes
        assert(duration <= 125000) // Allow some tolerance for test execution time
        assertEquals(10, session.totalChargePercentage)
        assertEquals(100.0, session.totalChargeMah, 0.01)
    }

    @Test
    fun `test session percentage charged calculation with statistics`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 25,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 20,
            totalChargeMah = 200.0
        )
        val currentPercentage = 45

        // When
        val percentageCharged = session.getPercentageCharged(currentPercentage)

        // Then
        assertEquals(20, percentageCharged) // 45 - 25 = 20
        assertEquals(20, session.totalChargePercentage) // Should match
    }

    @Test
    fun `test ended session percentage charged with statistics`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis() - 60000,
            endTimeEpochMillis = System.currentTimeMillis(),
            startPercentage = 30,
            endPercentage = 65,
            isActive = false,
            totalChargePercentage = 35,
            totalChargeMah = 350.0
        )

        // When
        val percentageCharged = session.percentageCharged

        // Then
        assertEquals(35, percentageCharged) // 65 - 30 = 35
        assertEquals(35, session.totalChargePercentage) // Should match
        assertEquals(350.0, session.totalChargeMah, 0.01)
    }

    @Test
    fun `test session with zero statistics`() {
        // Given
        val session = StatsChargeSession(
            startTimeEpochMillis = System.currentTimeMillis(),
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true,
            totalChargePercentage = 0,
            totalChargeMah = 0.0
        )

        // Then
        assertEquals(0, session.totalChargePercentage)
        assertEquals(0.0, session.totalChargeMah, 0.01)
        assertEquals(0, session.getPercentageCharged(50)) // No charge yet
    }
}
