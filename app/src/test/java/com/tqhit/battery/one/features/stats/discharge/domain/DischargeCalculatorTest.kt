package com.tqhit.battery.one.features.stats.discharge.domain

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for DischargeCalculator
 */
class DischargeCalculatorTest {

    private lateinit var calculator: DischargeCalculator

    @Before
    fun setUp() {
        calculator = DischargeCalculator()
    }

    @Test
    fun `estimateTimeRemainingMillis returns correct time for valid inputs`() {
        // Given
        val currentCapacityMah = 1500.0 // 50% of 3000mAh battery
        val dischargeRateMah = 300.0 // 300mA discharge rate

        // When
        val result = calculator.estimateTimeRemainingMillis(currentCapacityMah, dischargeRateMah)

        // Then
        val expectedHours = 1500.0 / 300.0 // 5 hours
        val expectedMillis = (expectedHours * 3600.0 * 1000.0).toLong()
        assertEquals(expectedMillis, result)
    }

    @Test
    fun `estimateTimeRemainingMillis returns zero for zero capacity`() {
        // Given
        val currentCapacityMah = 0.0
        val dischargeRateMah = 300.0

        // When
        val result = calculator.estimateTimeRemainingMillis(currentCapacityMah, dischargeRateMah)

        // Then
        assertEquals(0L, result)
    }

    @Test
    fun `estimateTimeRemainingMillis returns zero for zero discharge rate`() {
        // Given
        val currentCapacityMah = 1500.0
        val dischargeRateMah = 0.0

        // When
        val result = calculator.estimateTimeRemainingMillis(currentCapacityMah, dischargeRateMah)

        // Then
        assertEquals(0L, result)
    }

    @Test
    fun `calculateMixedDischargeRate returns correct weighted average`() {
        // Given
        val screenOnRate = 400.0
        val screenOffRate = 100.0

        // When
        val result = calculator.calculateMixedDischargeRate(screenOnRate, screenOffRate)

        // Then
        val expectedRate = (screenOnRate * DischargeCalculator.MIXED_USAGE_SCREEN_ON_WEIGHT) + 
                          (screenOffRate * DischargeCalculator.MIXED_USAGE_SCREEN_OFF_WEIGHT)
        assertEquals(expectedRate, result, 0.01)
    }

    @Test
    fun `calculateCurrentCapacityMah returns correct capacity`() {
        // Given
        val batteryPercentage = 75
        val totalCapacityMah = 4000.0

        // When
        val result = calculator.calculateCurrentCapacityMah(batteryPercentage, totalCapacityMah)

        // Then
        val expectedCapacity = (75.0 / 100.0) * 4000.0 // 3000mAh
        assertEquals(expectedCapacity, result, 0.01)
    }

    @Test
    fun `shouldSkipUpdate returns true for short time and same percentage`() {
        // Given
        val timeSinceLastUpdateMs = 500L // Less than 1 second
        val currentPercentage = 50
        val lastPercentage = 50

        // When
        val result = calculator.shouldSkipUpdate(timeSinceLastUpdateMs, currentPercentage, lastPercentage)

        // Then
        assertTrue(result)
    }

    @Test
    fun `shouldSkipUpdate returns false for percentage change`() {
        // Given
        val timeSinceLastUpdateMs = 500L // Less than 1 second
        val currentPercentage = 49
        val lastPercentage = 50

        // When
        val result = calculator.shouldSkipUpdate(timeSinceLastUpdateMs, currentPercentage, lastPercentage)

        // Then
        assertFalse(result)
    }

    @Test
    fun `shouldSkipUpdate returns false for sufficient time elapsed`() {
        // Given
        val timeSinceLastUpdateMs = 2000L // More than 1 second
        val currentPercentage = 50
        val lastPercentage = 50

        // When
        val result = calculator.shouldSkipUpdate(timeSinceLastUpdateMs, currentPercentage, lastPercentage)

        // Then
        assertFalse(result)
    }
}
