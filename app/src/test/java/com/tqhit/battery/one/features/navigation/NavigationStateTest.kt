package com.tqhit.battery.one.features.navigation

import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import org.junit.Test
import org.junit.Assert.*

class NavigationStateTest {

    @Test
    fun `createChargingState returns correct state`() {
        // Act
        val state = NavigationState.createChargingState()
        
        // Assert
        assertEquals(R.id.chargeFragment, state.activeFragmentId)
        assertTrue(state.isCharging)
        assertTrue(state.shouldShowTransition)
        assertTrue(state.isChargingState())
        assertFalse(state.isDischargingState())
        
        // Check visible menu items
        assertTrue(state.isMenuItemVisible(R.id.chargeFragment))
        assertTrue(state.isMenuItemVisible(R.id.healthFragment))
        assertTrue(state.isMenuItemVisible(R.id.settingsFragment))
        assertTrue(state.isMenuItemVisible(R.id.animationGridFragment))
        assertFalse(state.isMenuItemVisible(R.id.dischargeFragment))
    }

    @Test
    fun `createDischargingState returns correct state`() {
        // Act
        val state = NavigationState.createDischargingState()
        
        // Assert
        assertEquals(R.id.dischargeFragment, state.activeFragmentId)
        assertFalse(state.isCharging)
        assertTrue(state.shouldShowTransition)
        assertFalse(state.isChargingState())
        assertTrue(state.isDischargingState())
        
        // Check visible menu items
        assertTrue(state.isMenuItemVisible(R.id.dischargeFragment))
        assertTrue(state.isMenuItemVisible(R.id.healthFragment))
        assertTrue(state.isMenuItemVisible(R.id.settingsFragment))
        assertTrue(state.isMenuItemVisible(R.id.animationGridFragment))
        assertFalse(state.isMenuItemVisible(R.id.chargeFragment))
    }

    @Test
    fun `createChargingState with no transition returns correct state`() {
        // Act
        val state = NavigationState.createChargingState(shouldShowTransition = false)
        
        // Assert
        assertEquals(R.id.chargeFragment, state.activeFragmentId)
        assertTrue(state.isCharging)
        assertFalse(state.shouldShowTransition)
    }

    @Test
    fun `createDischargingState with no transition returns correct state`() {
        // Act
        val state = NavigationState.createDischargingState(shouldShowTransition = false)
        
        // Assert
        assertEquals(R.id.dischargeFragment, state.activeFragmentId)
        assertFalse(state.isCharging)
        assertFalse(state.shouldShowTransition)
    }

    @Test
    fun `createDefaultState returns charging state without transition`() {
        // Act
        val state = NavigationState.createDefaultState()
        
        // Assert
        assertEquals(R.id.chargeFragment, state.activeFragmentId)
        assertTrue(state.isCharging)
        assertFalse(state.shouldShowTransition)
    }

    @Test
    fun `getFragmentClass returns correct fragment classes`() {
        // Arrange
        val chargingState = NavigationState.createChargingState()
        val dischargingState = NavigationState.createDischargingState()
        val healthState = chargingState.copy(activeFragmentId = R.id.healthFragment)
        val settingsState = chargingState.copy(activeFragmentId = R.id.settingsFragment)
        val animationState = chargingState.copy(activeFragmentId = R.id.animationGridFragment)
        
        // Act & Assert
        assertEquals(StatsChargeFragment::class.java, chargingState.getFragmentClass())
        assertEquals(DischargeFragment::class.java, dischargingState.getFragmentClass())
        assertEquals(HealthFragment::class.java, healthState.getFragmentClass())
        assertEquals(SettingsFragment::class.java, settingsState.getFragmentClass())
        assertEquals(AnimationGridFragment::class.java, animationState.getFragmentClass())
    }

    @Test
    fun `createFragment returns correct fragment instances`() {
        // Arrange
        val chargingState = NavigationState.createChargingState()
        val dischargingState = NavigationState.createDischargingState()
        val healthState = chargingState.copy(activeFragmentId = R.id.healthFragment)
        val settingsState = chargingState.copy(activeFragmentId = R.id.settingsFragment)
        val animationState = chargingState.copy(activeFragmentId = R.id.animationGridFragment)
        
        // Act & Assert
        assertTrue(chargingState.createFragment() is StatsChargeFragment)
        assertTrue(dischargingState.createFragment() is DischargeFragment)
        assertTrue(healthState.createFragment() is HealthFragment)
        assertTrue(settingsState.createFragment() is SettingsFragment)
        assertTrue(animationState.createFragment() is AnimationGridFragment)
    }

    @Test
    fun `getFragmentClass returns default for unknown fragment id`() {
        // Arrange
        val unknownState = NavigationState(
            activeFragmentId = 999999, // Unknown ID
            visibleMenuItems = emptyList(),
            isCharging = true
        )
        
        // Act & Assert
        assertEquals(StatsChargeFragment::class.java, unknownState.getFragmentClass())
    }

    @Test
    fun `createFragment returns default for unknown fragment id`() {
        // Arrange
        val unknownState = NavigationState(
            activeFragmentId = 999999, // Unknown ID
            visibleMenuItems = emptyList(),
            isCharging = true
        )
        
        // Act & Assert
        assertTrue(unknownState.createFragment() is StatsChargeFragment)
    }

    @Test
    fun `always visible items are included in both states`() {
        // Arrange
        val chargingState = NavigationState.createChargingState()
        val dischargingState = NavigationState.createDischargingState()
        
        // Act & Assert
        NavigationState.ALWAYS_VISIBLE_ITEMS.forEach { itemId ->
            assertTrue("Item $itemId should be visible in charging state", 
                chargingState.isMenuItemVisible(itemId))
            assertTrue("Item $itemId should be visible in discharging state", 
                dischargingState.isMenuItemVisible(itemId))
        }
    }

    @Test
    fun `all menu items constant contains expected items`() {
        // Assert
        val expectedItems = listOf(
            R.id.chargeFragment,
            R.id.dischargeFragment,
            R.id.healthFragment,
            R.id.settingsFragment,
            R.id.animationGridFragment
        )
        
        assertEquals(expectedItems.size, NavigationState.ALL_MENU_ITEMS.size)
        expectedItems.forEach { itemId ->
            assertTrue("ALL_MENU_ITEMS should contain $itemId", 
                NavigationState.ALL_MENU_ITEMS.contains(itemId))
        }
    }

    @Test
    fun `always visible items constant contains expected items`() {
        // Assert
        val expectedItems = listOf(
            R.id.healthFragment,
            R.id.settingsFragment,
            R.id.animationGridFragment
        )
        
        assertEquals(expectedItems.size, NavigationState.ALWAYS_VISIBLE_ITEMS.size)
        expectedItems.forEach { itemId ->
            assertTrue("ALWAYS_VISIBLE_ITEMS should contain $itemId", 
                NavigationState.ALWAYS_VISIBLE_ITEMS.contains(itemId))
        }
    }
}

class NavigationStateChangeTest {

    @Test
    fun `NavigationStateChange holds correct data`() {
        // Arrange
        val previousState = NavigationState.createChargingState()
        val newState = NavigationState.createDischargingState()
        val reason = StateChangeReason.CHARGING_STOPPED
        
        // Act
        val stateChange = NavigationStateChange(previousState, newState, reason)
        
        // Assert
        assertEquals(previousState, stateChange.previousState)
        assertEquals(newState, stateChange.newState)
        assertEquals(reason, stateChange.reason)
    }

    @Test
    fun `NavigationStateChange can have null previous state`() {
        // Arrange
        val newState = NavigationState.createChargingState()
        val reason = StateChangeReason.INITIAL_SETUP
        
        // Act
        val stateChange = NavigationStateChange(null, newState, reason)
        
        // Assert
        assertNull(stateChange.previousState)
        assertEquals(newState, stateChange.newState)
        assertEquals(reason, stateChange.reason)
    }
}

class StateChangeReasonTest {

    @Test
    fun `StateChangeReason enum contains expected values`() {
        // Assert
        val expectedReasons = listOf(
            StateChangeReason.INITIAL_SETUP,
            StateChangeReason.CHARGING_STARTED,
            StateChangeReason.CHARGING_STOPPED,
            StateChangeReason.USER_NAVIGATION,
            StateChangeReason.APP_RESUME,
            StateChangeReason.FRAGMENT_RESTORE
        )
        
        val actualReasons = StateChangeReason.values().toList()
        assertEquals(expectedReasons.size, actualReasons.size)
        expectedReasons.forEach { reason ->
            assertTrue("StateChangeReason should contain $reason", actualReasons.contains(reason))
        }
    }
}
