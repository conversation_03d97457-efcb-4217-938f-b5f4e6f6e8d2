package com.tqhit.battery.one.features.stats.discharge.domain

import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.flow.MutableStateFlow
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for ScreenTimeValidationService
 */
class ScreenTimeValidationServiceTest {
    
    private lateinit var appLifecycleManager: AppLifecycleManager
    private lateinit var validationService: ScreenTimeValidationService
    
    @Before
    fun setUp() {
        // Mock ProcessLifecycleOwner to prevent actual lifecycle registration
        mockkStatic(ProcessLifecycleOwner::class)
        val mockProcessLifecycleOwner = mockk<ProcessLifecycleOwner>(relaxed = true)
        every { ProcessLifecycleOwner.get() } returns mockProcessLifecycleOwner

        appLifecycleManager = mockk()
        validationService = ScreenTimeValidationService(appLifecycleManager)
    }
    
    @Test
    fun `validateScreenTimes returns NoValidationNeeded when session is null`() {
        // Arrange
        val screenOnTime = 30000L // 30 seconds
        val screenOffTime = 60000L // 60 seconds
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = null
        )
        
        // Assert
        assertEquals(ValidationResult.NoValidationNeeded, result)
    }
    
    @Test
    fun `validateScreenTimes returns NoValidationNeeded when session is inactive`() {
        // Arrange
        val screenOnTime = 30000L
        val screenOffTime = 60000L
        val inactiveSession = createMockSession(isActive = false, durationMs = 90000L)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = inactiveSession
        )
        
        // Assert
        assertEquals(ValidationResult.NoValidationNeeded, result)
    }
    
    @Test
    fun `validateScreenTimes returns Valid when times are within acceptable range`() {
        // Arrange
        val screenOnTime = 30000L // 30 seconds
        val screenOffTime = 60000L // 60 seconds
        val totalTime = screenOnTime + screenOffTime // 90 seconds
        val sessionDuration = 95000L // 95 seconds (5 second gap, within tolerance)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert
        assertTrue(result is ValidationResult.Valid)
        val validResult = result as ValidationResult.Valid
        assertEquals(screenOnTime, validResult.screenOnTime)
        assertEquals(screenOffTime, validResult.screenOffTime)
    }
    
    @Test
    fun `validateScreenTimes corrects OFF time when gap exceeds tolerance`() {
        // Arrange
        val screenOnTime = 30000L // 30 seconds
        val screenOffTime = 40000L // 40 seconds
        val sessionDuration = 120000L // 120 seconds (50 second gap, exceeds tolerance)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(screenOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(90000L, correctedResult.correctedScreenOffTime) // 120s - 30s = 90s
        assertEquals(CorrectionType.OFF_TIME_ADJUSTED, correctedResult.correctionType)
    }
    
    @Test
    fun `validateScreenTimes applies proportional scaling for significant excess`() {
        // Arrange
        val screenOnTime = 60000L // 60 seconds
        val screenOffTime = 60000L // 60 seconds
        val totalTime = screenOnTime + screenOffTime // 120 seconds
        val sessionDuration = 100000L // 100 seconds (20% excess, triggers proportional scaling)
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(CorrectionType.PROPORTIONAL_SCALED, correctedResult.correctionType)
        
        // Check that scaling was applied (should be approximately 100/120 = 0.833 factor)
        val expectedScaleFactor = sessionDuration.toDouble() / totalTime.toDouble()
        val expectedOnTime = (screenOnTime * expectedScaleFactor).toLong()
        val expectedOffTime = (screenOffTime * expectedScaleFactor).toLong()
        
        assertEquals(expectedOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(expectedOffTime, correctedResult.correctedScreenOffTime)
    }
    
    @Test
    fun `validateScreenTimes handles zero screen OFF time correctly`() {
        // Arrange
        val screenOnTime = 90000L // 90 seconds
        val screenOffTime = 0L // 0 seconds
        val sessionDuration = 120000L // 120 seconds
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession
        )
        
        // Assert
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(screenOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(30000L, correctedResult.correctedScreenOffTime) // 120s - 90s = 30s
        assertEquals(CorrectionType.OFF_TIME_ADJUSTED, correctedResult.correctionType)
    }
    
    @Test
    fun `validateScreenTimes forces correction when shouldForceCorrection is true`() {
        // Arrange
        val screenOnTime = 30000L
        val screenOffTime = 60000L
        val sessionDuration = 95000L // Small gap that normally wouldn't trigger correction
        val activeSession = createMockSession(isActive = true, durationMs = sessionDuration)
        
        // Act
        val result = validationService.validateScreenTimes(
            screenOnTimeUI = screenOnTime,
            screenOffTimeUI = screenOffTime,
            sessionData = activeSession,
            shouldForceCorrection = true
        )
        
        // Assert
        assertTrue(result is ValidationResult.Corrected)
        val correctedResult = result as ValidationResult.Corrected
        assertEquals(screenOnTime, correctedResult.correctedScreenOnTime)
        assertEquals(65000L, correctedResult.correctedScreenOffTime) // 95s - 30s = 65s
    }
    
    @Test
    fun `shouldTriggerUiUpdate delegates to AppLifecycleManager`() {
        // Arrange
        every { appLifecycleManager.shouldTriggerUiUpdate() } returns true
        
        // Act
        val result = validationService.shouldTriggerUiUpdate()
        
        // Assert
        assertTrue(result)
    }
    
    @Test
    fun `getAppStateInfo delegates to AppLifecycleManager`() {
        // Arrange
        val expectedInfo = "App: FOREGROUND, Fragment: ACTIVE"
        every { appLifecycleManager.getCurrentStateInfo() } returns expectedInfo
        
        // Act
        val result = validationService.getAppStateInfo()
        
        // Assert
        assertEquals(expectedInfo, result)
    }
    
    private fun createMockSession(isActive: Boolean, durationMs: Long): DischargeSessionData {
        return mockk<DischargeSessionData>().apply {
            every { <EMAIL> } returns isActive
            every { <EMAIL> } returns durationMs
        }
    }
}
