package com.tqhit.battery.one.features.stats.charge

import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for CalculateSimpleChargeEstimateUseCase.
 * Tests various scenarios for charge time estimation.
 */
class CalculateSimpleChargeEstimateUseCaseTest {
    
    private val useCase = CalculateSimpleChargeEstimateUseCase()
    
    @Test
    fun `test estimate when not charging returns zero`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 50,
            isCharging = false,
            currentMicroAmperes = -500000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f
        )
        
        // When
        val result = useCase.execute(status, null, 3000, 100)
        
        // Then
        assertEquals(0L, result)
    }
    
    @Test
    fun `test estimate when already at target returns zero`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 100,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        // When
        val result = useCase.execute(status, null, 3000, 100)
        
        // Then
        assertEquals(0L, result)
    }
    
    @Test
    fun `test estimate when above target returns zero`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 85,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        // When
        val result = useCase.execute(status, null, 3000, 80)
        
        // Then
        assertEquals(0L, result)
    }
    
    @Test
    fun `test estimate with default rate when no session`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 50,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        // When - charging from 50% to 100% (50% to charge)
        val result = useCase.execute(status, null, 3000, 100)
        
        // Then - with default rate of 20%/h, 50% should take 2.5 hours = 9,000,000 ms
        val expectedTimeMs = (50.0 / 20.0) * 60 * 60 * 1000
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test estimate with session-based rate`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val sessionStartTime = currentTime - (60 * 60 * 1000) // 1 hour ago
        
        val status = StatsChargeStatus(
            percentage = 70,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        val session = StatsChargeSession(
            startTimeEpochMillis = sessionStartTime,
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true
        )
        
        // When - charging from 70% to 100% (30% to charge)
        // Session charged 20% in 1 hour = 20%/h rate
        val result = useCase.execute(status, session, 3000, 100)
        
        // Then - with session rate of 20%/h, 30% should take 1.5 hours = 5,400,000 ms
        val expectedTimeMs = (30.0 / 20.0) * 60 * 60 * 1000
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test estimate ignores session if too short`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val sessionStartTime = currentTime - (2 * 60 * 1000) // 2 minutes ago (too short)
        
        val status = StatsChargeStatus(
            percentage = 55,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        val session = StatsChargeSession(
            startTimeEpochMillis = sessionStartTime,
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val result = useCase.execute(status, session, 3000, 100)
        
        // Then - should use default rate since session is too short
        val expectedTimeMs = (45.0 / 20.0) * 60 * 60 * 1000 // 45% to charge at 20%/h
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test estimate ignores session if no percentage charged`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val sessionStartTime = currentTime - (30 * 60 * 1000) // 30 minutes ago
        
        val status = StatsChargeStatus(
            percentage = 50, // Same as start percentage
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        val session = StatsChargeSession(
            startTimeEpochMillis = sessionStartTime,
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true
        )
        
        // When
        val result = useCase.execute(status, session, 3000, 100)
        
        // Then - should use default rate since no percentage charged
        val expectedTimeMs = (50.0 / 20.0) * 60 * 60 * 1000 // 50% to charge at 20%/h
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test calculateTimeToFull method`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 75,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        // When
        val result = useCase.calculateTimeToFull(status, null, 3000)
        
        // Then - 25% to charge at 20%/h = 1.25 hours = 4,500,000 ms
        val expectedTimeMs = (25.0 / 20.0) * 60 * 60 * 1000
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test calculateTimeToTarget method`() {
        // Given
        val status = StatsChargeStatus(
            percentage = 60,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        // When
        val result = useCase.calculateTimeToTarget(status, null, 3000, 85)
        
        // Then - 25% to charge at 20%/h = 1.25 hours = 4,500,000 ms
        val expectedTimeMs = (25.0 / 20.0) * 60 * 60 * 1000
        assertEquals(expectedTimeMs.toLong(), result)
    }
    
    @Test
    fun `test estimate with very fast session rate is capped`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val sessionStartTime = currentTime - (10 * 60 * 1000) // 10 minutes ago
        
        val status = StatsChargeStatus(
            percentage = 90,
            isCharging = true,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4200,
            temperatureCelsius = 25.0f
        )
        
        val session = StatsChargeSession(
            startTimeEpochMillis = sessionStartTime,
            endTimeEpochMillis = null,
            startPercentage = 50,
            endPercentage = null,
            isActive = true
        )
        
        // When - session charged 40% in 10 minutes = 240%/h (unreasonable)
        val result = useCase.execute(status, session, 3000, 100)
        
        // Then - should fall back to default rate since session rate is unreasonable
        val expectedTimeMs = (10.0 / 20.0) * 60 * 60 * 1000 // 10% to charge at 20%/h
        assertEquals(expectedTimeMs.toLong(), result)
    }
}
