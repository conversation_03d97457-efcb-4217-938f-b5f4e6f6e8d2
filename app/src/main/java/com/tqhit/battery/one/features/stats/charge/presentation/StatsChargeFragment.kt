package com.tqhit.battery.one.features.stats.charge.presentation

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.SeekBar
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.material.progressindicator.CircularProgressIndicator
import com.tqhit.battery.one.R
import com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment for displaying charge statistics and estimates.
 * Shows current battery status, active charge session, and time estimates.
 */
@AndroidEntryPoint
class StatsChargeFragment : Fragment() {

    companion object {
        private const val TAG = "StatsChargeFragment"

        fun newInstance(): StatsChargeFragment {
            return StatsChargeFragment()
        }
    }

    private val viewModel: StatsChargeViewModel by viewModels()
    private val appViewModel: AppViewModel by viewModels()

    @Inject
    lateinit var vibrationService: VibrationService
    
    // UI Views
    private lateinit var tvPercentage: TextView
    private lateinit var circularProgressIndicator: CircularProgressIndicator
    private lateinit var tvChargingStatus: TextView
    private lateinit var tvCurrent: TextView
    private lateinit var tvVoltage: TextView
    private lateinit var tvTemperature: TextView
    private lateinit var tvPower: TextView
    
    private lateinit var tvSessionStartTime: TextView
    private lateinit var tvSessionDuration: TextView
    private lateinit var tvSessionPercentageCharged: TextView
    private lateinit var tvSessionTotalChargeMah: TextView
    
    private lateinit var tvTimeToFull: TextView
    private lateinit var tvTimeToTarget: TextView
    private lateinit var tvTargetPercentage: TextView
    private lateinit var seekBarTarget: SeekBar
    
    private lateinit var btnResetSession: Button
    private lateinit var batteryAlarmBtn: TextView

    private val permissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                showBatteryAlarmDialog()
            } else {
                NotificationDialog(
                    requireActivity(),
                    getString(R.string.notification),
                    getString(R.string.notify_access)
                ).show()
            }
        }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onCreateView() started at $startTime")

        val view = inflater.inflate(R.layout.fragment_stats_charge, container, false)

        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onCreateView() completed in ${System.currentTimeMillis() - startTime}ms")
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)

        val initViewsStartTime = System.currentTimeMillis()
        initializeViews(view)
        Log.d(TAG, "STARTUP_TIMING: initializeViews() took ${System.currentTimeMillis() - initViewsStartTime}ms")

        val setupStartTime = System.currentTimeMillis()
        setupSeekBar()
        setupResetButton()
        setupBatteryAlarmButton()
        Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")

        val observeStartTime = System.currentTimeMillis()
        observeUiState()
        Log.d(TAG, "STARTUP_TIMING: observeUiState() took ${System.currentTimeMillis() - observeStartTime}ms")

        Log.d(TAG, "StatsChargeFragment view created")
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }
    
    /**
     * Initializes all UI views.
     */
    private fun initializeViews(view: View) {
        // Battery status views
        tvPercentage = view.findViewById(R.id.tv_percentage)
        circularProgressIndicator = view.findViewById(R.id.charge_prog_bar_percent)
        tvChargingStatus = view.findViewById(R.id.tv_charging_status)
        tvCurrent = view.findViewById(R.id.tv_current)
        tvVoltage = view.findViewById(R.id.tv_voltage)
        tvTemperature = view.findViewById(R.id.tv_temperature)
        tvPower = view.findViewById(R.id.tv_power)
        
        // Session views
        tvSessionStartTime = view.findViewById(R.id.tv_session_start_time)
        tvSessionDuration = view.findViewById(R.id.tv_session_duration)
        tvSessionPercentageCharged = view.findViewById(R.id.tv_session_percentage_charged)
        tvSessionTotalChargeMah = view.findViewById(R.id.tv_session_total_charge_mah)
        
        // Estimate views
        tvTimeToFull = view.findViewById(R.id.tv_time_to_full)
        tvTimeToTarget = view.findViewById(R.id.tv_time_to_target)
        tvTargetPercentage = view.findViewById(R.id.tv_target_percentage)
        seekBarTarget = view.findViewById(R.id.seekbar_target)
        
        // Control views
        btnResetSession = view.findViewById(R.id.btn_reset_session)
        batteryAlarmBtn = view.findViewById(R.id.battery_alarm_btn)
    }
    
    /**
     * Sets up the target percentage SeekBar.
     */
    private fun setupSeekBar() {
        seekBarTarget.min = 1
        seekBarTarget.max = 100
        
        seekBarTarget.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    tvTargetPercentage.text = "${progress}%"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // No action needed
            }
            
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                seekBar?.let { bar ->
                    viewModel.setTargetChargePercentage(bar.progress)
                    Log.d(TAG, "Target percentage set to: ${bar.progress}%")
                }
            }
        })
    }
    
    /**
     * Sets up the reset session button.
     */
    private fun setupResetButton() {
        btnResetSession.setOnClickListener {
            viewModel.resetChargeSession()
            Log.d(TAG, "Reset session button clicked")
        }
    }

    /**
     * Sets up the battery alarm button.
     */
    private fun setupBatteryAlarmButton() {
        batteryAlarmBtn.setOnClickListener {
            showBatteryAlarmDialog()
        }
    }

    /**
     * Shows the battery alarm dialog with permission handling.
     */
    private fun showBatteryAlarmDialog() {
        if (PermissionUtils.isNotificationPermissionGranted(requireContext())) {
            SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show()
        } else {
            PermissionUtils.requestNotificationPermission(
                context = requireContext(),
                permissionLauncher = permissionLauncher,
                onPermissionGranted = { SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show() },
                onPermissionDenied = { NotificationDialog(requireActivity(), getString(R.string.notification), getString(R.string.notify_access)).show() }
            )
        }
    }
    
    /**
     * Observes UI state changes and updates the views.
     */
    private fun observeUiState() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    updateUI(uiState)
                }
            }
        }
    }
    
    /**
     * Updates all UI views with the current state.
     */
    private fun updateUI(uiState: StatsChargeUiState) {
        Log.v(TAG, "Updating UI with state: charging=${uiState.status?.isCharging}, " +
            "percentage=${uiState.status?.percentage}%, " +
            "sessionActive=${uiState.session?.isActive}")
        
        updateBatteryStatusViews(uiState)
        updateSessionViews(uiState)
        updateEstimateViews(uiState)
        updateTargetPercentage(uiState)
    }
    
    /**
     * Updates battery status related views.
     */
    private fun updateBatteryStatusViews(uiState: StatsChargeUiState) {
        val status = uiState.status

        if (status != null) {
            tvPercentage.text = "${status.percentage}%"
            circularProgressIndicator.progress = status.percentage
            tvChargingStatus.text = if (status.isCharging) "Charging" else "Not Charging"
            tvCurrent.text = viewModel.formatCurrent(status.currentMicroAmperes)
            tvVoltage.text = viewModel.formatVoltage(status.voltageMillivolts)
            tvTemperature.text = viewModel.formatTemperature(status.temperatureCelsius)
            tvPower.text = viewModel.formatPower(uiState.powerWatts)
        } else {
            tvPercentage.text = "N/A"
            circularProgressIndicator.progress = 0
            tvChargingStatus.text = "Unknown"
            tvCurrent.text = "N/A"
            tvVoltage.text = "N/A"
            tvTemperature.text = "N/A"
            tvPower.text = "N/A"
        }
    }
    
    /**
     * Updates session related views.
     */
    private fun updateSessionViews(uiState: StatsChargeUiState) {
        val session = uiState.session
        val status = uiState.status
        
        if (session != null && session.isActive) {
            // Format start time
            val startTime = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date(session.startTimeEpochMillis))
            tvSessionStartTime.text = startTime
            
            // Format duration
            tvSessionDuration.text = viewModel.formatTime(session.durationMillis)

            // Calculate and display percentage charged
            val percentageCharged = if (status != null) {
                session.getPercentageCharged(status.percentage)
            } else {
                session.percentageCharged
            }
            tvSessionPercentageCharged.text = "${percentageCharged}%"

            // Display total charge mAh
            tvSessionTotalChargeMah.text = String.format("%.1fmAh", session.totalChargeMah)
            
            btnResetSession.isEnabled = true
        } else {
            tvSessionStartTime.text = "-"
            tvSessionDuration.text = "-"
            tvSessionPercentageCharged.text = "-"
            tvSessionTotalChargeMah.text = "-"
            btnResetSession.isEnabled = false
        }
    }
    
    /**
     * Updates estimate related views.
     */
    private fun updateEstimateViews(uiState: StatsChargeUiState) {
        tvTimeToFull.text = viewModel.formatTime(uiState.timeToFullMillis)
        tvTimeToTarget.text = viewModel.formatTime(uiState.timeToTargetMillis)
    }
    
    /**
     * Updates target percentage related views.
     */
    private fun updateTargetPercentage(uiState: StatsChargeUiState) {
        if (seekBarTarget.progress != uiState.targetPercentage) {
            seekBarTarget.progress = uiState.targetPercentage
        }
        tvTargetPercentage.text = "${uiState.targetPercentage}%"
    }
}
