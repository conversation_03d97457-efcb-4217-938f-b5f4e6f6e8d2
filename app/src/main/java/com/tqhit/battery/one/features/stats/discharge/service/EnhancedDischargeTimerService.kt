package com.tqhit.battery.one.features.stats.discharge.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService
import com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Enhanced discharge timer service following CoreBatteryStatsService architecture pattern
 * Implements robust timer service logic with gap estimation caching and improved reliability
 */
@AndroidEntryPoint
class EnhancedDischargeTimerService : Service() {

    companion object {
        private const val TAG = "EnhancedDischargeTimer"
        private const val NOTIFICATION_ID = 103
        private const val CHANNEL_ID = "EnhancedDischargeTimerChannel"
        private const val CHANNEL_NAME = "Enhanced Discharge Timer"
        private const val UI_UPDATE_INTERVAL_MS = 1000L // 1 second update interval
        private const val SCREEN_STATE_CHECK_INTERVAL_MS = 5000L // 5 seconds for screen state check
        private const val VALIDATION_CHECK_INTERVAL_MS = 10000L // 10 seconds for validation check
        private const val RELIABILITY_CHECK_INTERVAL_MS = 30000L // 30 seconds for reliability check
        
        // Service actions
        const val ACTION_START_SERVICE = "com.tqhit.battery.one.START_ENHANCED_DISCHARGE_TIMER"
        const val ACTION_STOP_SERVICE = "com.tqhit.battery.one.STOP_ENHANCED_DISCHARGE_TIMER"
    }

    @Inject
    lateinit var dischargeSessionRepository: DischargeSessionRepository

    @Inject
    lateinit var screenTimeValidationService: ScreenTimeValidationService

    @Inject
    lateinit var appLifecycleManager: AppLifecycleManager

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private var isTracking = false
    private var trackingStartTime = 0L
    private lateinit var powerManager: PowerManager
    private var lastScreenStateCheck = 0L
    private var timerUpdateCount = 0L

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "Enhanced discharge timer service created at ${System.currentTimeMillis()}")
        
        powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "Service start command received: action=${intent?.action}, flags=$flags, startId=$startId")
        
        when (intent?.action) {
            ACTION_START_SERVICE -> {
                if (!isTracking) {
                    startEnhancedTracking()
                    startForeground(NOTIFICATION_ID, createNotification())
                    Log.i(TAG, "Enhanced tracking started in foreground mode")
                } else {
                    Log.i(TAG, "Enhanced tracking already active, ignoring start command")
                }
            }
            ACTION_STOP_SERVICE -> {
                stopEnhancedTracking()
                stopSelf()
                Log.i(TAG, "Enhanced tracking stopped via action")
            }
            else -> {
                // Default behavior for backward compatibility
                if (!isTracking) {
                    startEnhancedTracking()
                    startForeground(NOTIFICATION_ID, createNotification())
                    Log.i(TAG, "Enhanced tracking started (default action)")
                }
            }
        }
        
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        Log.i(TAG, "Enhanced discharge timer service destroyed")
        stopEnhancedTracking()
        super.onDestroy()
    }

    /**
     * Start enhanced tracking with robust timer service logic
     */
    private fun startEnhancedTracking() {
        Log.i(TAG, "Starting enhanced screen time tracking at ${System.currentTimeMillis()}")
        isTracking = true
        trackingStartTime = System.currentTimeMillis()
        lastScreenStateCheck = System.currentTimeMillis()
        timerUpdateCount = 0L
        
        // Main UI update timer with enhanced logic
        serviceScope.launch {
            Log.i(TAG, "ENHANCED_TIMER: Starting main UI update loop")
            while (isTracking) {
                try {
                    val session = dischargeSessionRepository.currentSession.value
                    if (session != null && session.isActive) {
                        // Use enhanced increment method with robust timer logic
                        val (onTime, offTime) = dischargeSessionRepository.incrementScreenTimeForUI()

                        // Perform continuous validation and apply corrections if needed
                        performContinuousValidation(session, onTime, offTime)

                        timerUpdateCount++

                        // Log every 10 seconds to avoid noise
                        if (timerUpdateCount % 10 == 0L) {
                            Log.d(TAG, "ENHANCED_TIMER: Update #$timerUpdateCount - ON: ${onTime/1000}s, OFF: ${offTime/1000}s, ${screenTimeValidationService.getAppStateInfo()}")
                        }
                    } else {
                        // Log every 10 seconds when no active session
                        if (timerUpdateCount % 10 == 0L) {
                            Log.d(TAG, "ENHANCED_TIMER: No active session, skipping increment")
                        }
                        timerUpdateCount++
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in enhanced timer update", e)
                }
                
                delay(UI_UPDATE_INTERVAL_MS)
            }
            Log.i(TAG, "ENHANCED_TIMER: Main UI update loop stopped")
        }
        
        // Enhanced screen state verification with gap validation
        serviceScope.launch {
            Log.i(TAG, "ENHANCED_TIMER: Starting screen state verification and gap validation loop")
            while (isTracking) {
                try {
                    delay(SCREEN_STATE_CHECK_INTERVAL_MS)

                    val session = dischargeSessionRepository.currentSession.value
                    if (session != null && session.isActive) {
                        val currentTime = System.currentTimeMillis()

                        // Perform enhanced screen state check
                        dischargeSessionRepository.forceCheckScreenState()

                        // NEW: Perform dedicated gap validation during Screen OFF periods
                        performGapValidation(session)

                        lastScreenStateCheck = currentTime
                        Log.v(TAG, "ENHANCED_TIMER: Screen state verification and gap validation completed")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in enhanced screen state verification and gap validation", e)
                }
            }
            Log.i(TAG, "ENHANCED_TIMER: Screen state verification and gap validation loop stopped")
        }
        
        // Service reliability monitoring
        serviceScope.launch {
            Log.i(TAG, "ENHANCED_TIMER: Starting reliability monitoring loop")
            while (isTracking) {
                try {
                    delay(RELIABILITY_CHECK_INTERVAL_MS)
                    
                    val currentTime = System.currentTimeMillis()
                    val trackingDuration = currentTime - trackingStartTime
                    val timeSinceLastStateCheck = currentTime - lastScreenStateCheck
                    
                    Log.d(TAG, "RELIABILITY_CHECK: Service running for ${trackingDuration/1000}s, " +
                          "last state check ${timeSinceLastStateCheck/1000}s ago, " +
                          "updates: $timerUpdateCount")
                    
                    // Check for potential issues
                    if (timeSinceLastStateCheck > SCREEN_STATE_CHECK_INTERVAL_MS * 3) {
                        Log.w(TAG, "RELIABILITY_WARNING: Screen state check delayed by ${timeSinceLastStateCheck/1000}s")
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Error in reliability monitoring", e)
                }
            }
            Log.i(TAG, "ENHANCED_TIMER: Reliability monitoring loop stopped")
        }
    }

    /**
     * Stop enhanced tracking
     */
    private fun stopEnhancedTracking() {
        Log.i(TAG, "Stopping enhanced screen time tracking at ${System.currentTimeMillis()}")
        if (isTracking) {
            val totalTrackingTime = System.currentTimeMillis() - trackingStartTime
            Log.d(TAG, "Total enhanced tracking time: ${totalTrackingTime/1000} seconds, updates: $timerUpdateCount")
        }
        isTracking = false
    }

    /**
     * Performs continuous validation of screen times during discharge
     * This runs regardless of screen state and ensures UI updates when needed
     */
    private fun performContinuousValidation(session: DischargeSessionData, onTime: Long, offTime: Long) {
        try {
            // Use the new validation service for comprehensive validation
            val validationResult = screenTimeValidationService.validateScreenTimes(
                screenOnTimeUI = onTime,
                screenOffTimeUI = offTime,
                sessionData = session,
                shouldForceCorrection = screenTimeValidationService.shouldTriggerUiUpdate()
            )

            when (validationResult) {
                is ValidationResult.Corrected -> {
                    Log.i(TAG, "CONTINUOUS_VALIDATION: Applying correction - " +
                          "ON: ${onTime/1000}s → ${validationResult.correctedScreenOnTime/1000}s, " +
                          "OFF: ${offTime/1000}s → ${validationResult.correctedScreenOffTime/1000}s, " +
                          "Type: ${validationResult.correctionType}")

                    // Apply the correction through the repository
                    dischargeSessionRepository.applyScreenTimeGapCorrection(validationResult.correctedScreenOffTime)
                }
                is ValidationResult.Valid -> {
                    Log.v(TAG, "CONTINUOUS_VALIDATION: Times are valid")
                }
                ValidationResult.NoValidationNeeded -> {
                    Log.v(TAG, "CONTINUOUS_VALIDATION: No validation needed")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in continuous validation", e)
        }
    }

    /**
     * Performs dedicated gap validation during Screen OFF periods
     * Implements the 60-second tolerance requirement (legacy method for compatibility)
     */
    private fun performGapValidation(session: DischargeSessionData) {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            val isScreenOn = powerManager.isInteractive

            // Only perform gap validation during Screen OFF periods as per requirements
            if (!isScreenOn) {
                val (uiOnTime, uiOffTime) = dischargeSessionRepository.incrementScreenTimeForUI()

                // Use the new validation service for consistency
                val validationResult = screenTimeValidationService.validateScreenTimes(
                    screenOnTimeUI = uiOnTime,
                    screenOffTimeUI = uiOffTime,
                    sessionData = session,
                    shouldForceCorrection = true // Force validation during screen OFF
                )

                when (validationResult) {
                    is ValidationResult.Corrected -> {
                        Log.i(TAG, "GAP_VALIDATION: Applying correction during Screen OFF - " +
                              "ON: ${uiOnTime/1000}s → ${validationResult.correctedScreenOnTime/1000}s, " +
                              "OFF: ${uiOffTime/1000}s → ${validationResult.correctedScreenOffTime/1000}s")

                        // Apply the correction through the repository
                        dischargeSessionRepository.applyScreenTimeGapCorrection(validationResult.correctedScreenOffTime)
                    }
                    else -> {
                        Log.v(TAG, "GAP_VALIDATION: No correction needed during Screen OFF")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in gap validation", e)
        }
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Enhanced discharge timer service notifications"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Notification channel created: $CHANNEL_ID")
        }
    }

    /**
     * Create foreground service notification
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Enhanced Discharge Tracking")
            .setContentText("Monitoring battery discharge with improved reliability")
            .setSmallIcon(R.drawable.ic_discharge_icon)
            .setOngoing(true)
            .setShowWhen(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
}
