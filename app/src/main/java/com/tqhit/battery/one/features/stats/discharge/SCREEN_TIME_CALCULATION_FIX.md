# Screen Time Calculation Fix - Mathematical Constraint Issue

## Problem Description

**Issue**: The sum of "Screen On time" + "Screen Off time" was greater than the "Current session Total time" displayed in the Current Session Details section. This is mathematically impossible since screen time should never exceed the total session duration.

**Symptoms**:
- UI showing screen times that don't add up correctly
- Screen On + Screen Off > Total Session Duration
- Inconsistent time displays between different UI sections

## Root Cause Analysis

### **The Core Problem: Dual Time Tracking Systems**

The application was running **two separate time tracking systems** in parallel:

1. **Session Data Times** (`DischargeSessionData.screenOnTimeMillis` / `screenOffTimeMillis`):
   - Updated only when battery status changes or screen state changes
   - Used for "Current Session Details" total duration calculation
   - Calculated as `lastUpdateTimeEpochMillis - startTimeEpochMillis`

2. **UI Tracker Times** (`ScreenStateTimeTracker.screenOnTimeUI` / `screenOffTimeUI`):
   - Updated every second via `DischargeTimerService.incrementScreenTimeForUI()`
   - Used for "Loss of Charge" section display
   - Incremented continuously regardless of session updates

### **The Mathematical Impossibility**

- **UI Display**: "Loss of Charge" section used `state.screenOnTimeUI + state.screenOffTimeUI`
- **Session Display**: "Current Session Details" used `session.durationMillis`
- **Timer Service**: Called `incrementScreenTimeForUI()` every 1000ms
- **Session Updates**: Only occurred on battery/screen events (infrequent)

This caused UI times to accumulate faster than session duration, violating the fundamental constraint: **Screen Time Sum ≤ Session Duration**.

## Solution Implemented

### **1. Real-Time Session Duration Calculation**

**File**: `DischargeSessionData.kt`

```kotlin
val durationMillis: Long
    get() = if (isActive) {
        System.currentTimeMillis() - startTimeEpochMillis  // Real-time for active sessions
    } else {
        lastUpdateTimeEpochMillis - startTimeEpochMillis   // Fixed for ended sessions
    }
```

**Impact**: Session duration now updates in real-time for active sessions, preventing the time lag that caused the constraint violation.

### **2. Mathematical Constraint Enforcement**

**File**: `DischargeSessionRepository.kt` - `incrementScreenTimeForUI()` method

```kotlin
// Enforce mathematical constraint: screen times cannot exceed session duration
val currentSession = _currentSession.value
val (constrainedOnTime, constrainedOffTime) = if (currentSession != null && currentSession.isActive) {
    val currentSessionDuration = System.currentTimeMillis() - currentSession.startTimeEpochMillis
    val totalScreenTime = rawOnTime + rawOffTime
    
    if (totalScreenTime > currentSessionDuration) {
        // Scale down the times proportionally to fit within session duration
        val scaleFactor = currentSessionDuration.toDouble() / totalScreenTime.toDouble()
        val adjustedOnTime = (rawOnTime * scaleFactor).toLong()
        val adjustedOffTime = (rawOffTime * scaleFactor).toLong()
        
        // Update the tracker with constrained values to prevent future drift
        screenStateTimeTracker.forceSetTimes(adjustedOnTime, adjustedOffTime)
        
        Pair(adjustedOnTime, adjustedOffTime)
    } else {
        Pair(rawOnTime, rawOffTime)
    }
} else {
    Pair(rawOnTime, rawOffTime)
}
```

**Impact**: 
- **Prevents constraint violations** by scaling down times proportionally
- **Maintains proportions** between screen on/off times
- **Self-corrects drift** by updating the tracker with constrained values

### **3. Enhanced ScreenStateTimeTracker**

**File**: `ScreenStateTimeTracker.kt`

Added `forceSetTimes()` method:
```kotlin
fun forceSetTimes(onTimeMs: Long, offTimeMs: Long) {
    Log.i(TAG, "Force setting times - ON: ${onTimeMs/1000}s, OFF: ${offTimeMs/1000}s")
    
    _screenOnTimeUI.value = onTimeMs
    _screenOffTimeUI.value = offTimeMs
    
    // Reset increment time to prevent immediate drift
    lastIncrementTime = System.currentTimeMillis()
}
```

**Impact**: Allows the repository to correct time drift by directly setting constrained values.

### **4. UI Validation and Logging**

**File**: `DischargeUiUpdater.kt`

```kotlin
// Validate mathematical constraint: screen time sum ≤ session duration
if (totalScreenTime > sessionDuration) {
    Log.w(TAG, "MATH_CONSTRAINT_VIOLATION: Screen time sum (${totalScreenTime/1000}s) exceeds session duration (${sessionDuration/1000}s)")
}
```

**Impact**: Provides early detection and logging of constraint violations for debugging.

### **5. Comprehensive Consistency Verification**

**File**: `DischargeSessionRepository.kt` - `verifyScreenTimeConsistency()` method

```kotlin
// Check mathematical constraint: screen time sum ≤ session duration
if (uiTotalTime > sessionDuration) {
    Log.e(TAG, "MATH_CONSTRAINT_VIOLATION: UI screen time sum exceeds session duration!")
}
```

**Impact**: Continuous monitoring and automatic correction of inconsistencies.

## Testing

### **Unit Tests Created**

**File**: `ScreenTimeConstraintTest.kt`

1. **`test session duration calculation with real-time updates`**
   - Verifies real-time duration calculation for active sessions
   - Ensures screen time sum ≤ session duration

2. **`test constraint enforcement with proportional scaling`**
   - Tests the scaling logic when constraint would be violated
   - Verifies proportional scaling maintains time ratios

3. **`test inactive session duration calculation`**
   - Ensures fixed duration calculation for ended sessions
   - Verifies perfect time matching for completed sessions

4. **`test constraint validation in UI updater`**
   - Tests UI-level constraint detection
   - Ensures validation logic is exercised

### **Test Results**
All tests pass, confirming the fix works correctly.

## Expected Behavior After Fix

### **✅ Mathematical Constraint Always Satisfied**
- Screen On Time + Screen Off Time ≤ Total Session Duration
- No more impossible time calculations

### **✅ Real-Time Session Duration**
- Active sessions show current duration in real-time
- Ended sessions show fixed duration from completion time

### **✅ Proportional Time Scaling**
- When constraint would be violated, times are scaled proportionally
- Original time ratios are preserved during scaling

### **✅ Self-Healing System**
- Automatic detection and correction of time drift
- Continuous consistency verification

### **✅ Enhanced Logging**
- Detailed logging for constraint violations
- Monitoring of scaling operations
- Debugging information for time inconsistencies

## Monitoring and Debugging

### **Log Tags to Monitor**
- `SCREEN_TIME_CONSTRAINT`: Constraint enforcement actions
- `MATH_CONSTRAINT_VIOLATION`: Detected violations
- `SCREEN_TIME_RESYNC`: Automatic re-synchronization
- `TIMING_DEBUG`: Detailed timing information

### **Key Metrics to Watch**
- Frequency of constraint violations (should be rare after fix)
- Scaling operations (indicates timing issues)
- Time consistency verification results

## Future Improvements

1. **Predictive Constraint Prevention**: Detect potential violations before they occur
2. **Advanced Time Synchronization**: More sophisticated sync between UI and session data
3. **Performance Optimization**: Reduce frequency of constraint checks for better performance
4. **User Feedback**: Add UI indicators when time estimation vs. real-time tracking is used

## Summary

This fix ensures that the fundamental mathematical constraint **Screen Time Sum ≤ Session Duration** is always satisfied by:

1. **Real-time session duration calculation** for active sessions
2. **Proportional scaling** when constraint would be violated  
3. **Continuous monitoring** and automatic correction
4. **Enhanced logging** for debugging and monitoring

The solution maintains data integrity while providing a consistent and mathematically correct user experience.
