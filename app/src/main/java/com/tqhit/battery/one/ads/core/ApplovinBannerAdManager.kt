package com.tqhit.battery.one.ads.core

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.ViewGroup
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxAdRevenueListener
//import com.applovin.mediation.MaxAdViewAdListener
//import com.applovin.mediation.ads.MaxAdView
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinBannerAdManager @Inject constructor(
    private val analyticsTracker: AnalyticsTracker
//) : MaxAdRevenueListener {
) {
//    private val adUnitId = "d25a1beaccf7bd55"
//
//    // Track impressionData by placement
//    private val placementImpressions = mutableMapOf<String, MutableList<MaxAd>>()
//    private val lastLogTimes = mutableMapOf<String, Long>()
//    private val LOG_INTERVAL = 180_000L // 180 seconds in milliseconds
//    private val handler = Handler(Looper.getMainLooper())
//    private val timerRunnable = object : Runnable {
//        override fun run() {
//            checkAndLogByTime()
//            handler.postDelayed(this, 5_000L) // check every 5 seconds
//        }
//    }
//
//    init {
//        handler.postDelayed(timerRunnable, 5_000L)
//    }
//
//    fun createAd(): MaxAdView {
//        val adView = MaxAdView(adUnitId)
//        adView.layoutParams = ViewGroup.LayoutParams(
//            ViewGroup.LayoutParams.MATCH_PARENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        return adView
//    }
//
//    fun loadAd(
//        placement: String,
//        adView: MaxAdView,
//        listener: MaxAdViewAdListener,
//    ) {
//        adView.setListener(listener)
//        adView.setRevenueListener(this)
//        adView.placement = placement
//        adView.loadAd()
//    }
//
//    fun destroy(
//        adView: MaxAdView
//    ) {
//        adView.destroy()
//    }
//
//    private fun logPlacement(placement: String, impressions: List<MaxAd>) {
//        val totalRevenue = impressions.sumOf { it.revenue }
//        val impressionCount = impressions.size
//        val adUnitId = impressions.firstOrNull()?.adUnitId ?: ""
//        val format = impressions.firstOrNull()?.format?.label ?: ""
//        val networkName = impressions.firstOrNull()?.networkName ?: ""
//        analyticsTracker.logEvent(
//            FirebaseAnalytics.Event.AD_IMPRESSION,
//            mapOf(
//                FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
//                FirebaseAnalytics.Param.AD_UNIT_NAME to adUnitId,
//                FirebaseAnalytics.Param.AD_FORMAT to format,
//                FirebaseAnalytics.Param.AD_SOURCE to networkName,
//                FirebaseAnalytics.Param.VALUE to totalRevenue,
//                FirebaseAnalytics.Param.CURRENCY to "USD",
//                "placement" to placement,
//                "impression_count" to impressionCount,
//            )
//        )
//        analyticsTracker.logEvent(
//            "ad_impression_custom",
//            mapOf(
//                FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
//                FirebaseAnalytics.Param.AD_UNIT_NAME to adUnitId,
//                FirebaseAnalytics.Param.AD_FORMAT to format,
//                FirebaseAnalytics.Param.AD_SOURCE to networkName,
//                FirebaseAnalytics.Param.VALUE to totalRevenue,
//                FirebaseAnalytics.Param.CURRENCY to "USD",
//                "placement" to placement,
//                "impression_count" to impressionCount,
//            )
//        )
//        // Update last log time
//        lastLogTimes[placement] = System.currentTimeMillis()
//    }
//
//    private fun checkAndLogByTime() {
//        val now = System.currentTimeMillis()
//        val placementsToLog = placementImpressions.filter { (placement, impressions) ->
//            impressions.isNotEmpty() &&
//            (now - (lastLogTimes[placement] ?: 0L) >= LOG_INTERVAL)
//        }.keys
//        for (placement in placementsToLog) {
//            val impressions = placementImpressions[placement] ?: continue
//            logPlacement(placement, impressions)
//            placementImpressions[placement]?.clear()
//        }
//    }
//
//    override fun onAdRevenuePaid(impressionData: MaxAd) {
//        val placement = impressionData.placement
//        val impressions = placementImpressions.getOrPut(placement) { mutableListOf() }
//        impressions.add(impressionData)
//        // If this placement has 5 or more impressions, log and clear
//        if (impressions.size >= 5) {
//            logPlacement(placement, impressions)
//            impressions.clear()
//        }
//    }
}