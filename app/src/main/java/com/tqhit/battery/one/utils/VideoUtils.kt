package com.tqhit.battery.one.utils

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URL
import androidx.core.net.toUri
import com.tqhit.battery.one.service.ChargingOverlayService

object VideoUtils {
    /**
     * Downloads or copies a video file from a URL or local URI to the app's internal storage.
     * Returns the destination File if successful, or throws an Exception.
     */
    suspend fun downloadAndSaveVideo(context: Context, videoUrl: String): File = withContext(Dispatchers.IO) {
        val fileName = ChargingOverlayService.VIDEO_NAME
        val destFile = File(context.filesDir, fileName)
        if (videoUrl.startsWith("http://") || videoUrl.startsWith("https://")) {
            // Download from network
            try {
                URL(videoUrl).openStream().use { input ->
                    destFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
            } catch (e: Exception) {
                throw Exception("Failed to download video: ${e.message}")
            }
        } else {
            // Local file or content URI
            val inputUri = videoUrl.toUri()
            val inputStream = context.contentResolver.openInputStream(inputUri)
                ?: throw Exception("Cannot open input stream")
            inputStream.use { input ->
                destFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
        }
        destFile
    }
} 