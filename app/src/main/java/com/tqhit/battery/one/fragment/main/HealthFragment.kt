package com.tqhit.battery.one.fragment.main

import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentHealthBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

enum class HistoryType {
    PERCENTAGE,
    TEMPERATURE
}

@AndroidEntryPoint
class HealthFragment : AdLibBaseFragment<FragmentHealthBinding>() {
    override val binding by lazy { FragmentHealthBinding.inflate(layoutInflater) }
    private val batteryViewModel: BatteryViewModel by viewModels()

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    private var useCumulativeWear = true
    private var chartUpdateJob: Job? = null
    private var selectedHour: Int = 4

    override fun setupData() {
        super.setupData()
        observeBatteryHealth()
        updateChart(HistoryType.PERCENTAGE, 4)
        updateChart(HistoryType.TEMPERATURE, 4)
        updateDailyWearChart()
    }

    override fun setupListener() {
        super.setupListener()
        setupInfoBlockListeners()
        setupButtonListeners()
    }

    override fun setupUI() {
        super.setupUI()
        updateButtonWearCycle()
        setupChartListeners()
        // Set default 4h selection for both charts
        updateChart(HistoryType.PERCENTAGE, 4)
        updateChart(HistoryType.TEMPERATURE, 4)
    }

    override fun onStart() {
        super.onStart()
        startChartAutoUpdate()
    }

    override fun onStop() {
        super.onStop()
        chartUpdateJob?.cancel()
    }

    private fun startChartAutoUpdate() {
        chartUpdateJob?.cancel()
        chartUpdateJob =
                lifecycleScope.launch {
                    while (isActive) {
                        updateChart(HistoryType.PERCENTAGE, selectedHour)
                        updateChart(HistoryType.TEMPERATURE, selectedHour)
                        delay(60_000) // 1 minute
                    }
                }
    }

    private fun updateButtonWearCycle() {
        binding.cumulativeBtn.background =
                ContextCompat.getDrawable(
                        requireContext(),
                        if (useCumulativeWear) R.drawable.grey_block_selected_line_up_left
                        else R.drawable.grey_block_line_up_left
                )
        binding.singularBtn.background =
                ContextCompat.getDrawable(
                        requireContext(),
                        if (useCumulativeWear) R.drawable.grey_block_line_up_right
                        else R.drawable.grey_block_selected_line_up_right
                )
        binding.methodText.text =
                if (useCumulativeWear) getString(R.string.cumulative_text)
                else getString(R.string.singular_text)
        binding.cumulativeSessionInfo.isInvisible = !useCumulativeWear
        binding.singularSessionInfo.isInvisible = useCumulativeWear
        binding.healthCountOfSessionsSingular.text = getString(R.string.singular_no_data)
    }

    private fun updateCalculatedWear(value: Double) {
        val context = binding.root.context
        val percentStr = "$value"
        val fullText =
                context.getString(com.tqhit.battery.one.R.string.calculated_for, value.toString())
        val start = fullText.indexOf(percentStr)
        val end = start + percentStr.length
        val spannable = SpannableString(fullText)
        val greenColor = getThemeColor(requireContext(), com.tqhit.battery.one.R.attr.colorr)
        spannable.setSpan(
                ForegroundColorSpan(greenColor),
                start,
                end,
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.healthCountOfSessionsCumulative.text = spannable
    }

    private fun getThemeColor(context: android.content.Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }

    private fun observeBatteryHealth() {
        lifecycleScope.launch {
            batteryViewModel.batteryHealth.collect { health ->
                binding.healthFirstProgressbarCumulative.progress = health
                binding.healthPercentDamageCumulative.text = "$health"
            }
        }

        lifecycleScope.launch {
            batteryViewModel.totalChargeSession.collect { sessions ->
                binding.healthCheckedBateryCapacityCumulative.text =
                        (batteryViewModel.batteryHealth.value *
                                        batteryViewModel.batteryCapacity.value / 100)
                                .toInt()
                                .toString()
                val context = binding.root.context
                val sessionsStr = "$sessions"
                val fullText = context.getString(R.string.calculated_for, sessions.toString())
                val start = fullText.indexOf(sessionsStr)
                val end = start + sessionsStr.length
                val spannable = SpannableString(fullText)
                val greenColor = getThemeColor(requireContext(), R.attr.colorr)
                spannable.setSpan(
                        ForegroundColorSpan(greenColor),
                        start,
                        end,
                        android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                binding.healthCountOfSessionsCumulative.text = spannable
            }
        }

        lifecycleScope.launch {
            batteryViewModel.batteryCapacity.collect { capacity ->
                binding.healthFullBateryCapacity.text = capacity.toString()
            }
        }
    }

    // region UI Listeners
    private fun setupInfoBlockListeners() {
        binding.degreeWearInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.wear_rate, R.string.degree_of_wear_info)
            }
        }
    }

    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
        NotificationDialog(requireContext(), getString(titleResId), getString(messageResId)).show()
    }

    private fun setupButtonListeners() {
        binding.cumulativeBtn.setOnClickListener {
            useCumulativeWear = true
            updateButtonWearCycle()
        }

        binding.singularBtn.setOnClickListener {
            useCumulativeWear = false
            updateButtonWearCycle()
        }
    }

    private fun setupChartListeners() {
        // Percentage chart buttons
        binding.btn0.setOnClickListener {
            selectedHour = 4
            updateChart(HistoryType.PERCENTAGE, selectedHour)
        }
        binding.btn1.setOnClickListener {
            selectedHour = 8
            updateChart(HistoryType.PERCENTAGE, selectedHour)
        }
        binding.btn2.setOnClickListener {
            selectedHour = 16
            updateChart(HistoryType.PERCENTAGE, selectedHour)
        }
        binding.btn3.setOnClickListener {
            selectedHour = 24
            updateChart(HistoryType.PERCENTAGE, selectedHour)
        }
        // Temperature chart buttons
        binding.btn0T.setOnClickListener {
            selectedHour = 4
            updateChart(HistoryType.TEMPERATURE, selectedHour)
        }
        binding.btn1T.setOnClickListener {
            selectedHour = 8
            updateChart(HistoryType.TEMPERATURE, selectedHour)
        }
        binding.btn2T.setOnClickListener {
            selectedHour = 16
            updateChart(HistoryType.TEMPERATURE, selectedHour)
        }
        binding.btn3T.setOnClickListener {
            selectedHour = 24
            updateChart(HistoryType.TEMPERATURE, selectedHour)
        }
    }

    private fun updateChart(type: HistoryType, hours: Int) {
        updateDayLabels(hours, type)
        val history =
                when (type) {
                    HistoryType.PERCENTAGE -> batteryViewModel.getHistoryBatteryForHours(hours)
                    HistoryType.TEMPERATURE -> batteryViewModel.getHistoryTemperatureForHours(hours)
                }
        val chart =
                when (type) {
                    HistoryType.PERCENTAGE -> binding.chartPercent
                    HistoryType.TEMPERATURE -> binding.chart1
                }
        if (history.isEmpty()) {
            chart.clear()
            chart.setNoDataText("No chart data available.")
            chart.invalidate()
            return
        }

        val currentTime = System.currentTimeMillis()
        val startTime = currentTime - (hours * 60 * 60 * 1000)

        // Use milliseconds (timestamp) as x value
        val entries =
                history.map { (timestamp, value) -> Entry(timestamp.toFloat(), value.toFloat()) }

        val dataSet =
                LineDataSet(entries, type.name).apply {
                    lineWidth = 2f
                    setDrawCircles(false)
                    setDrawValues(false)
                    color = getThemeColor(requireContext(), com.tqhit.battery.one.R.attr.colorr)
                    mode = LineDataSet.Mode.CUBIC_BEZIER
                    setDrawFilled(true)
                    fillDrawable =
                            ContextCompat.getDrawable(requireContext(), R.drawable.chart_fill)
                    fillAlpha = 120
                }
        chart.data = LineData(dataSet)
        
        // Get max temperature value and round up to nearest 5
        val maxTemp = if (type == HistoryType.TEMPERATURE) {
            entries.maxOfOrNull { it.y } ?: 100f
        } else {
            null
        }
        val roundedMax = if (maxTemp != null) {
            ((maxTemp / 5f).toInt() * 5 + 5).toFloat()
        } else {
            null
        }
        
        // Set fixed y-axis values for percentage chart
        val yAxisValues = if (type == HistoryType.PERCENTAGE) {
            Pair(10f, 90f) // Fixed range from 10% to 90%
        } else {
            null
        }
        
        styleLineChart(chart, startTime, currentTime, roundedMax, yAxisValues)
        
        // Update y-axis labels for temperature chart
        if (type == HistoryType.TEMPERATURE && roundedMax != null) {
            val yAxisLabels = listOf(
                binding.t9Temp,
                binding.t8Temp,
                binding.t7Temp,
                binding.t6Temp,
                binding.t5Temp,
                binding.t4Temp,
                binding.t3Temp,
                binding.t2Temp,
                binding.t1Temp
            )
            
            // Calculate step size for y-axis labels
            val step = roundedMax / 8f // Divide by 8 to get 9 points
            
            // Update y-axis labels from bottom to top
            yAxisLabels.forEachIndexed { index, label ->
                val value = step * index
                label.text = String.format("%.1f°C", value)
            }
        }
        
        chart.invalidate()
    }

    private fun styleLineChart(
            chart: com.github.mikephil.charting.charts.LineChart,
            startTime: Long,
            endTime: Long,
            maxY: Float? = null,
            yAxisRange: Pair<Float, Float>? = null
    ) {
        chart.setDrawGridBackground(false)
        chart.setDrawBorders(false)
        chart.description.isEnabled = false
        chart.legend.isEnabled = false

        // Disable touch gestures
        chart.setTouchEnabled(false)
        chart.isDragEnabled = false
        chart.setScaleEnabled(false)
        chart.setPinchZoom(false)

        // X Axis
        val xAxis = chart.xAxis
        xAxis.isEnabled = true
        xAxis.axisMinimum = startTime.toFloat()
        xAxis.axisMaximum = endTime.toFloat()
        xAxis.setDrawLabels(false) // Hide default labels, since you use custom ones below the chart
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)

        // Y Axis
        val yAxisLeft = chart.axisLeft
        yAxisLeft.isEnabled = false // Completely disable Y axis
        chart.axisRight.isEnabled = false
        
        // Set Y axis values based on type
        if (yAxisRange != null) {
            // For percentage chart
            chart.axisLeft.axisMinimum = yAxisRange.first
            chart.axisLeft.axisMaximum = yAxisRange.second
            chart.axisRight.axisMinimum = yAxisRange.first
            chart.axisRight.axisMaximum = yAxisRange.second
        } else if (maxY != null) {
            // For temperature chart
            chart.axisLeft.axisMinimum = 0f
            chart.axisLeft.axisMaximum = maxY
            chart.axisRight.axisMinimum = 0f
            chart.axisRight.axisMaximum = maxY
        }
    }

    private fun updateDayLabels(hours: Int, type: HistoryType = HistoryType.PERCENTAGE) {
        val calendar = Calendar.getInstance()
        val currentTime = calendar.timeInMillis
        val startTime = currentTime - (hours * 60 * 60 * 1000)

        // Calculate time interval based on hours
        val intervalMinutes =
                when (hours) {
                    4 -> 30 // 30 minutes for 4 hours
                    8 -> 60 // 1 hour for 8 hours
                    16 -> 120 // 2 hours for 16 hours
                    24 -> 180 // 3 hours for 24 hours
                    else -> 60
                }

        // Update each day label
        for (i in 1..7) {
            val timeAgo = startTime + (i * intervalMinutes * 60 * 1000)
            calendar.timeInMillis = timeAgo

            val hour = calendar.get(Calendar.HOUR_OF_DAY)
            val minute = calendar.get(Calendar.MINUTE)

            val timeText =
                    when {
                        minute == 0 -> String.format("%d:00", hour)
                        else -> String.format("%d:%02d", hour, minute)
                    }

            when (type) {
                HistoryType.PERCENTAGE -> {
                    when (i) {
                        1 -> binding.day7Percent.text = timeText
                        2 -> binding.day6Percent.text = timeText
                        3 -> binding.day5Percent.text = timeText
                        4 -> binding.day4Percent.text = timeText
                        5 -> binding.day3Percent.text = timeText
                        6 -> binding.day2Percent.text = timeText
                        7 -> binding.day1Percent.text = timeText
                    }
                }
                HistoryType.TEMPERATURE -> {
                    when (i) {
                        1 -> binding.day7Temp.text = timeText
                        2 -> binding.day6Temp.text = timeText
                        3 -> binding.day5Temp.text = timeText
                        4 -> binding.day4Temp.text = timeText
                        5 -> binding.day3Temp.text = timeText
                        6 -> binding.day2Temp.text = timeText
                        7 -> binding.day1Temp.text = timeText
                    }
                }
            }
        }
    }

    private fun updateDailyWearChart() {
        val wearData = batteryViewModel.getDailyWearData(7)
        val progressBars =
                listOf(
                        binding.progbar1,
                        binding.progbar2,
                        binding.progbar3,
                        binding.progbar4,
                        binding.progbar5,
                        binding.progbar6,
                        binding.progbar7
                )

        // Find the maximum wear value to scale the progress bars
        val maxWear = wearData.maxOrNull() ?: 1.0

        // Update progress bars with scaled values (in reverse order)
        wearData.forEachIndexed { index, wear ->
            val progress = ((wear / maxWear) * 100).toInt()
            progressBars[6 - index].progress = progress
        }

        // Update day labels (in reverse order)
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("dd.MM", Locale.getDefault())

        for (i in 0..6) {
            calendar.timeInMillis = System.currentTimeMillis() - ((6 - i) * 24 * 60 * 60 * 1000)
            val dayLabel =
                    when (i) {
                        0 -> binding.day7
                        1 -> binding.day6
                        2 -> binding.day5
                        3 -> binding.day4
                        4 -> binding.day3
                        5 -> binding.day2
                        6 -> binding.day1
                        else -> null
                    }
            dayLabel?.text = dateFormat.format(calendar.time)
        }

        // Add y-axis labels (from top to bottom)
        val yAxisLabels =
                listOf(
                        binding.t9,
                        binding.t8,
                        binding.t7,
                        binding.t6,
                        binding.t5,
                        binding.t4,
                        binding.t3,
                        binding.t2,
                        binding.t1
                )

        // Calculate step size for y-axis labels
        val step = maxWear / 8 // Divide by 8 to get 9 points (0 to 8)
        yAxisLabels.forEachIndexed { index, label ->
            val value = step * index
            label.text = String.format("%.2f", value)
        }
    }
    // endregion
}
