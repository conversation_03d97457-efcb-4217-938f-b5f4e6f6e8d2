package com.tqhit.battery.one.dialog.utils

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogNotificationBinding
import dagger.hilt.android.qualifiers.ActivityContext

class NotificationDialog(
    @ActivityContext private val context: Context,
    private val title: String,
    private val message: String,
    private val onConfirm: () -> Unit = {},
    private val onCancel: () -> Unit = {}
) : AdLibBaseDialog<DialogNotificationBinding>(context) {
    override val binding by lazy { DialogNotificationBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupUI() {
        super.setupUI()

        binding.upText.text = title
        binding.mainText.text = message
    }

    override fun setupListener() {
        super.setupListener()

        binding.confirmChangeCapacityError.setOnClickListener {
            dismiss()
            onConfirm()
        }

        binding.exit.setOnClickListener {
            onCancel()
            dismiss()
        }
    }
}