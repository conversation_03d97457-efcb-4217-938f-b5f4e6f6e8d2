package com.tqhit.battery.one.features.stats.discharge.repository

import android.content.Context
import android.os.BatteryManager
import android.os.PowerManager
import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Repository for battery-related operations and data
 * Integrates with CoreBatteryStatsProvider for centralized battery monitoring
 */
@Singleton
class BatteryRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val appRepository: AppRepository,
    private val dischargeRatesCache: DischargeRatesCache,
    private val dischargeSessionRepository: DischargeSessionRepository
) {
    companion object {
        private const val TAG = "BatteryRepository"
        private const val DEFAULT_BATTERY_CAPACITY = 3000 // mAh
        private const val MAX_RATE_SAMPLES = 5 // e.g., for 2 minutes of data if sampled per second
    }

    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val powerManager by lazy { context.getSystemService(Context.POWER_SERVICE) as PowerManager }
    
    // Screen on/off discharge rate state
    private val _averageScreenOnDischargeRateMah = MutableStateFlow(DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA)
    val averageScreenOnDischargeRateMah: StateFlow<Double> = _averageScreenOnDischargeRateMah.asStateFlow()

    private val _averageScreenOffDischargeRateMah = MutableStateFlow(DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA)
    val averageScreenOffDischargeRateMah: StateFlow<Double> = _averageScreenOffDischargeRateMah.asStateFlow()

    // Sample collections for learning discharge rates
    private val screenOnCurrentSamples = ConcurrentLinkedQueue<Double>()
    private val screenOffCurrentSamples = ConcurrentLinkedQueue<Double>()

    /**
     * Flow of battery status from CoreBatteryStatsProvider
     */
    val batteryStatusFlow: Flow<CoreBatteryStatus> = coreBatteryStatsProvider.coreBatteryStatusFlow
        .filterNotNull()
        .distinctUntilChanged()
        .onEach { status ->
            Log.i(TAG, "REPO: Received core battery status: isCharging=${status.isCharging}, %=${status.percentage}, time=${status.timestampEpochMillis}")
            
            // Process battery status for discharge session tracking
            repositoryScope.launch {
                Log.d(TAG, "REPO: Forwarding status to dischargeSessionRepository: isCharging=${status.isCharging}")
                dischargeSessionRepository.processBatteryStatus(status)
            }
        }
        .shareIn(repositoryScope, SharingStarted.WhileSubscribed(5000), replay = 1)

    init {
        // Load cached discharge rates on initialization
        repositoryScope.launch {
            dischargeRatesCache.getAverageScreenOnRateMah()?.let { rate ->
                if (rate > 0) {
                    Log.d(TAG, "Loaded cached screen ON discharge rate: $rate mA")
                    _averageScreenOnDischargeRateMah.value = rate
                }
            }
            
            dischargeRatesCache.getAverageScreenOffRateMah()?.let { rate ->
                if (rate > 0) {
                    Log.d(TAG, "Loaded cached screen OFF discharge rate: $rate mA")
                    _averageScreenOffDischargeRateMah.value = rate
                }
            }
        }

        // Background coroutine to learn and update discharge rates
        repositoryScope.launch(Dispatchers.Default) {
            Log.d(TAG, "Starting background coroutine for discharge rate learning")
            batteryStatusFlow.collect { status ->
                Log.d(TAG, "COLLECTING STATUS: $status")
                if (!status.isCharging && status.currentMicroAmperes < -1000) { // Discharging significantly
                    val currentMa = abs(status.currentMicroAmperes / 1000.0)
                    val screenIsOn = powerManager.isInteractive // Capture it once
                    val timestamp = System.currentTimeMillis()
                    Log.d(TAG, "LEARNING SAMPLE: currentMa=${currentMa}mA, isScreenOn=${screenIsOn}, rawMicroAmps=${status.currentMicroAmperes}")
                    Log.d(TAG, "Processing discharge current: $currentMa mA, screen is ${if (powerManager.isInteractive) "ON" else "OFF"}")
                    
                    if (screenIsOn) { // Screen is ON
                        Log.d(TAG, "RATE_LEARN_CLASSIFY @ $timestamp: Attributing ${currentMa}mA to SCREEN_ON_SAMPLES")
                        addSampleAndUpdateRate(
                            currentMa,
                            screenOnCurrentSamples,
                            _averageScreenOnDischargeRateMah
                        ) { newRate ->
                            Log.d(TAG, "Learned new screen ON discharge rate: $newRate mA")
                            launch { dischargeRatesCache.saveAverageScreenOnRateMah(newRate) }
                        }
                    } else { // Screen is OFF
                        Log.d(TAG, "RATE_LEARN_CLASSIFY @ $timestamp: Attributing ${currentMa}mA to SCREEN_OFF_SAMPLES")
                        addSampleAndUpdateRate(
                            currentMa,
                            screenOffCurrentSamples,
                            _averageScreenOffDischargeRateMah
                        ) { newRate ->
                            Log.d(TAG, "Learned new screen OFF discharge rate: $newRate mA")
                            launch { dischargeRatesCache.saveAverageScreenOffRateMah(newRate) }
                        }
                    }
                }
            }
        }
    }

    /**
     * Gets the effective battery capacity in mAh
     * Priority: 1. User set capacity, 2. System reported capacity, 3. Default value
     */
    fun getEffectiveCapacityMah(): Int {
        // First check if user has set a custom capacity
        val userSetCapacity = appRepository.getBatteryCapacity()
        if (userSetCapacity > 0) {
            Log.d(TAG, "Using user-set battery capacity: $userSetCapacity mAh")
            return userSetCapacity
        }

        // Then try to get the capacity from BatteryUtils
        val utilsCapacity = BatteryUtils.getBatteryCapacity(context)
        if (utilsCapacity > 0) {
            Log.d(TAG, "Using utility-derived battery capacity: $utilsCapacity mAh")
            return utilsCapacity
        }

        // Then try to get the system-reported capacity
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        val systemDesignCapacityMicroAh = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_COUNTER)
        if (systemDesignCapacityMicroAh > 0) {
            val capacityMah = (systemDesignCapacityMicroAh / 1000).toInt()
            Log.d(TAG, "Using system-reported battery capacity: $capacityMah mAh")
            return capacityMah
        }
        
        // Fall back to default value
        Log.d(TAG, "Using default battery capacity: $DEFAULT_BATTERY_CAPACITY mAh")
        return DEFAULT_BATTERY_CAPACITY
    }

    /**
     * Adds a current sample and updates the discharge rate if necessary
     */
    private fun addSampleAndUpdateRate(
        currentMa: Double, //sample_to_add
        samplesList: ConcurrentLinkedQueue<Double>,
        rateFlow: MutableStateFlow<Double>,
        saveAction: suspend (Double) -> Unit
    ) {
        Log.d(TAG, "ADD_SAMPLE_RATE: Received sampleToAdd=${currentMa} for ${if (rateFlow == _averageScreenOnDischargeRateMah) "ON" else "OFF"} queue.")
        // Add the new sample
        samplesList.add(currentMa)
        
        // Keep the queue size within limits
        while (samplesList.size > MAX_RATE_SAMPLES) {
            samplesList.poll()
        }
        
        if (samplesList.isNotEmpty()) {
            // Calculate the new average
            val newRate = samplesList.average()
            Log.d(TAG, "Current samples count: ${samplesList.size}, calculated average rate: $newRate mA (current rate: ${rateFlow.value} mA)")
            
            // Only update if significantly different (to avoid too many updates)
            if (abs(newRate - rateFlow.value) > 0.5 && newRate > 0.0) {
                Log.d(TAG, "Updating discharge rate: ${rateFlow.value} mA -> $newRate mA")
                rateFlow.value = newRate
                repositoryScope.launch { saveAction(newRate) }
            }
            Log.d(TAG, "SAMPLES: ${if (rateFlow == _averageScreenOnDischargeRateMah) "ON" else "OFF"} Queue: [${samplesList.joinToString(", ")}], NewRate: $newRate")
        }
    }
}
