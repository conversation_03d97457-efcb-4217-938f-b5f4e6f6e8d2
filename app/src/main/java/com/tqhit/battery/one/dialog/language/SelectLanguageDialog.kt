package com.tqhit.battery.one.dialog.language

import android.app.Activity
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogSelectLanguageBinding
import com.tqhit.battery.one.viewmodel.AppViewModel

class SelectLanguageDialog(
    private val activity: Activity,
    private val appViewModel: AppViewModel
) : AdLibBaseDialog<DialogSelectLanguageBinding>(activity) {
    override val binding by lazy { DialogSelectLanguageBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupListener() {
        super.setupListener()
        setupLanguageButtons()
        setupCloseButton()
        updateSelectedLanguageUI()
    }

    private fun setupLanguageButtons() {
        // Setup click listeners for each language option
        binding.de.setOnClickListener { saveLanguage("de") } // German
        binding.nl.setOnClickListener { saveLanguage("nl") } // Dutch
        binding.en.setOnClickListener { saveLanguage("en") } // English
        binding.es.setOnClickListener { saveLanguage("es") } // Spanish
        binding.fr.setOnClickListener { saveLanguage("fr") } // French
        binding.it.setOnClickListener { saveLanguage("it") } // Italian
        binding.hu.setOnClickListener { saveLanguage("hu") } // Hungarian
        binding.pl.setOnClickListener { saveLanguage("pl") } // Polish
        binding.pt.setOnClickListener { saveLanguage("pt") } // Portuguese
        binding.ro.setOnClickListener { saveLanguage("ro") } // Romanian
        binding.tr.setOnClickListener { saveLanguage("tr") } // Turkish
        binding.ru.setOnClickListener { saveLanguage("ru") } // Russian
        binding.ua.setOnClickListener { saveLanguage("uk") } // Ukrainian
        binding.ar.setOnClickListener { saveLanguage("ar") } // Arabic
        binding.zh.setOnClickListener { saveLanguage("zh") } // Chinese
    }

    private fun setupCloseButton() {
        binding.exit.setOnClickListener {
            dismiss()
        }
    }

    private fun updateSelectedLanguageUI() {
        val currentLanguage = appViewModel.getLanguage()

        // Set backgrounds and selection state based on current language
        binding.de.apply {
            isSelected = currentLanguage == "de"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
        }

        binding.nl.apply {
            isSelected = currentLanguage == "nl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.en.apply {
            isSelected = currentLanguage == "en"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.es.apply {
            isSelected = currentLanguage == "es"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.fr.apply {
            isSelected = currentLanguage == "fr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.it.apply {
            isSelected = currentLanguage == "it"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.hu.apply {
            isSelected = currentLanguage == "hu"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pl.apply {
            isSelected = currentLanguage == "pl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pt.apply {
            isSelected = currentLanguage == "pt"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ro.apply {
            isSelected = currentLanguage == "ro"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.tr.apply {
            isSelected = currentLanguage == "tr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ru.apply {
            isSelected = currentLanguage == "ru"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ua.apply {
            isSelected = currentLanguage == "uk"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ar.apply {
            isSelected = currentLanguage == "ar"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.zh.apply {
            isSelected = currentLanguage == "zh"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
        }
    }

    private fun saveLanguage(languageCode: String) {
        // Save language using ViewModel
        appViewModel.setLanguage(languageCode)

        // Apply the new language
        appViewModel.setLocale(activity, languageCode)

        dismiss()
        activity.recreate()
    }
}