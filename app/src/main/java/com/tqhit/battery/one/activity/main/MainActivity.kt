package com.tqhit.battery.one.activity.main

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxAdViewAdListener
//import com.applovin.mediation.MaxError
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityMainBinding
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
// Legacy NewChargeFragment import removed - using StatsChargeFragment instead
// Legacy ChargeMonitorServiceHelper import removed - replaced by UnifiedBatteryNotificationService
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.service.BatteryMonitorService
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.utils.AntiThiefUtils
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
import com.tqhit.battery.one.utils.DeviceUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AdLibBaseActivity<ActivityMainBinding>() {
    override val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    // Use statsChargeRepository directly instead of BatteryViewModel to avoid crashes
    @Inject lateinit var statsChargeRepository: StatsChargeRepository
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var applovinBannerAdManager: ApplovinBannerAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    // Legacy chargeMonitorServiceHelper injection removed - replaced by UnifiedBatteryNotificationService
    @Inject lateinit var enhancedDischargeTimerServiceHelper: EnhancedDischargeTimerServiceHelper
    @Inject lateinit var dischargeSessionRepository: DischargeSessionRepository
    @Inject lateinit var unifiedBatteryNotificationServiceHelper: UnifiedBatteryNotificationServiceHelper
    @Inject lateinit var usageStatsPermissionManager: UsageStatsPermissionManager
    @Inject lateinit var dynamicNavigationManager: DynamicNavigationManager
    @Inject lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    private var isInitialFragmentSet = false
    private var currentSelectedItemId: Int = R.id.chargeFragment
    private var isFragmentSetupInProgress = false
    private val handler = Handler(Looper.getMainLooper())

    // Add a receiver for handling animation-related events on Xiaomi devices
    private val animationFixReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "android.intent.action.SCREEN_ON" ||
                intent?.action == "android.intent.action.USER_PRESENT") {
                // When screen turns on or user unlocks, refresh the UI to prevent animation issues
                if (DeviceUtils.isXiaomiDevice()) {
                    try {
                        Log.d(TAG, "Screen state changed, refreshing UI for Xiaomi device")
                        binding.root.invalidate()
                        binding.navHostFragment.invalidate()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error handling screen state change", e)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val createStartTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "RESTORATION_FLOW: onCreate() started - savedInstanceState=${savedInstanceState != null}")

        // Log detailed savedInstanceState information
        savedInstanceState?.let { bundle ->
            val savedItemId = bundle.getInt("selected_item_id", -1)
            val allKeys = bundle.keySet().joinToString(", ")
            Log.d(TAG, "RESTORATION_FLOW: savedInstanceState contains keys: [$allKeys]")
            Log.d(TAG, "RESTORATION_FLOW: saved selected_item_id=$savedItemId")

            // Only restore if it's a non-charging/discharging fragment (health, settings, animation)
            if (savedItemId == R.id.healthFragment || savedItemId == R.id.settingsFragment || savedItemId == R.id.animationGridFragment) {
                currentSelectedItemId = savedItemId
                Log.d(TAG, "RESTORATION_FLOW: Restored non-contextual fragment: $savedItemId")
            } else {
                Log.d(TAG, "RESTORATION_FLOW: Ignoring contextual fragment from savedState (id=$savedItemId), will determine based on battery state")
            }
        } ?: run {
            Log.d(TAG, "RESTORATION_FLOW: No savedInstanceState - fresh app start")
        }

        Log.d(TAG, "RESTORATION_FLOW: currentSelectedItemId after onCreate processing: $currentSelectedItemId")

        // Initialize usage stats permission manager early in lifecycle
        try {
            usageStatsPermissionManager.initializePermissionLauncher(this)
            Log.d(TAG, "RESTORATION_FLOW: UsageStatsPermissionManager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error initializing UsageStatsPermissionManager", e)
        }

        // Apply additional Xiaomi-specific handling if needed
        applyXiaomiSpecificHandling()

        Log.d(TAG, "RESTORATION_FLOW: onCreate() completed in ${System.currentTimeMillis() - createStartTime}ms")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("selected_item_id", binding.bottomView.selectedItemId)
    }

    /**
     * Sets up dynamic navigation manager for real-time charging state switching
     */
    private fun setupDynamicNavigation() {
        Log.d(TAG, "RESTORATION_FLOW: setupDynamicNavigation() called")

        try {
            // Initialize the dynamic navigation manager
            Log.d(TAG, "RESTORATION_FLOW: Initializing DynamicNavigationManager")
            Log.d(TAG, "RESTORATION_FLOW: fragmentContainerId=${binding.navHostFragment.id}")
            Log.d(TAG, "RESTORATION_FLOW: bottomNavigationView menu size=${binding.bottomView.menu.size()}")

            dynamicNavigationManager.initialize(
                fragmentManager = supportFragmentManager,
                bottomNavigationView = binding.bottomView,
                fragmentContainerId = binding.navHostFragment.id,
                lifecycleOwner = this
            )

            // Set up bottom navigation listener to work with dynamic navigation
            Log.d(TAG, "RESTORATION_FLOW: Setting up bottom navigation listener")
            binding.bottomView.setOnItemSelectedListener { item ->
                Log.d(TAG, "RESTORATION_FLOW: Navigation item selected: ${item.itemId}")

                // Let the dynamic navigation manager handle the navigation
                val handled = dynamicNavigationManager.handleUserNavigation(item.itemId)

                if (handled) {
                    currentSelectedItemId = item.itemId
                    Log.d(TAG, "RESTORATION_FLOW: Navigation handled by dynamic manager")
                } else {
                    Log.w(TAG, "RESTORATION_FLOW: Navigation not handled by dynamic manager, falling back to manual handling")
                    // Fallback to manual fragment switching for non-dynamic items
                    handleManualNavigation(item.itemId)
                }

                handled
            }

            Log.d(TAG, "RESTORATION_FLOW: Dynamic navigation setup completed")
            Log.d(TAG, "STARTUP_TIMING: Dynamic navigation setup completed in ${System.currentTimeMillis() - System.currentTimeMillis()}ms")
        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error setting up dynamic navigation", e)
        }
    }



    /**
     * Handles manual navigation for items not managed by dynamic navigation
     */
    private fun handleManualNavigation(itemId: Int) {
        val fragment = when (itemId) {
            R.id.chargeFragment -> StatsChargeFragment()
            R.id.dischargeFragment -> DischargeFragment()
            R.id.healthFragment -> HealthFragment()
            R.id.settingsFragment -> SettingsFragment()
            R.id.animationGridFragment -> AnimationGridFragment()
            else -> return
        }

        Log.d(TAG, "Manual navigation to: ${fragment.javaClass.simpleName}")
        supportFragmentManager
            .beginTransaction()
            .replace(binding.navHostFragment.id, fragment)
            .commit()

        currentSelectedItemId = itemId
    }

    /**
     * Optimized fragment setup to prevent multiple recreations and improve startup performance
     * Now works with dynamic navigation manager
     */
    private fun setupInitialFragmentOptimized(startTime: Long) {
        // Prevent multiple concurrent fragment setups
        if (isFragmentSetupInProgress || isInitialFragmentSet) {
            Log.d(TAG, "Fragment setup already in progress or completed, skipping")
            return
        }

        isFragmentSetupInProgress = true

        lifecycleScope.launch {
            try {
                // Wait for dynamic navigation manager to be initialized
                var retryCount = 0
                while (!dynamicNavigationManager.isInitialized() && retryCount < 10) {
                    kotlinx.coroutines.delay(50)
                    retryCount++
                }

                if (!dynamicNavigationManager.isInitialized()) {
                    Log.w(TAG, "Dynamic navigation manager not initialized, falling back to legacy setup")
                    setupLegacyInitialFragment(startTime)
                    return@launch
                }

                // Dynamic navigation manager will handle initial fragment setup
                // Just mark as initialized
                withContext(Dispatchers.Main) {
                    isInitialFragmentSet = true
                    Log.d(TAG, "STARTUP_TIMING: Dynamic navigation setup completed in ${System.currentTimeMillis() - startTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in dynamic fragment setup", e)
                setupLegacyInitialFragment(startTime)
            } finally {
                isFragmentSetupInProgress = false
            }
        }
    }

    /**
     * Legacy fragment setup as fallback
     */
    private fun setupLegacyInitialFragment(startTime: Long) {
        lifecycleScope.launch {
            try {
                val batteryStatusStartTime = System.currentTimeMillis()

                // Use background thread for battery status retrieval with timeout to avoid blocking UI
                val initialBatteryStatus = withContext(Dispatchers.IO) {
                    try {
                        // Get status from CoreBatteryStatsProvider with timeout
                        withTimeout(500) {
                            coreBatteryStatsProvider.getCurrentStatus()
                                ?: com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Timeout getting battery status from core provider", e)
                        // Return default status if timeout
                        com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.createDefault()
                    }
                }

                Log.d(TAG, "STARTUP_TIMING: Battery status retrieval took ${System.currentTimeMillis() - batteryStatusStartTime}ms")

                val isCharging = initialBatteryStatus.isCharging

                // Switch back to main thread for UI operations
                withContext(Dispatchers.Main) {
                    if (!isInitialFragmentSet) {
                        Log.d(TAG, "Legacy initial fragment setup - charging state: $isCharging")
                        val fragmentStartTime = System.currentTimeMillis()

                        val initialFragment = if (isCharging) StatsChargeFragment() else DischargeFragment()
                        val initialItemId = if (isCharging) R.id.chargeFragment else R.id.dischargeFragment

                        // Use commitNow() for immediate execution to prevent timing issues
                        supportFragmentManager
                            .beginTransaction()
                            .replace(binding.navHostFragment.id, initialFragment)
                            .commitNow()

                        binding.bottomView.selectedItemId = initialItemId
                        currentSelectedItemId = initialItemId
                        isInitialFragmentSet = true

                        Log.d(TAG, "STARTUP_TIMING: Legacy fragment setup took ${System.currentTimeMillis() - fragmentStartTime}ms")
                    }

                    Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() completed in ${System.currentTimeMillis() - startTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in legacy fragment setup", e)
            }
        }
    }

    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return activityManager.getRunningServices(Integer.MAX_VALUE)
                .any { it.service.className == serviceClass.name }
    }

    override fun setupUI() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: MainActivity.setupUI() started at $startTime")

        super.setupUI()
        Log.d(TAG, "setupUI: Setting up main UI")

        // Initialize dynamic navigation manager only if not already initialized
        if (!dynamicNavigationManager.isInitialized()) {
            Log.d(TAG, "RESTORATION_FLOW: DynamicNavigationManager not initialized, setting up")
            setupDynamicNavigation()
        } else {
            Log.d(TAG, "RESTORATION_FLOW: DynamicNavigationManager already initialized, skipping setup")
            // Force a state refresh to ensure proper navigation visibility
            dynamicNavigationManager.handleBatteryStateChange()
        }

        // Set initial fragment based on charging state only once with optimization
        setupInitialFragmentOptimized(startTime)

        // --- Banner Ad Integration ---
        initBannerAd()
    }

    private fun restoreFragmentState() {
        Log.d(TAG, "RESTORATION_FLOW: restoreFragmentState() called")
        Log.d(TAG, "RESTORATION_FLOW: State - isInitialFragmentSet=$isInitialFragmentSet")
        Log.d(TAG, "RESTORATION_FLOW: State - currentSelectedItemId=$currentSelectedItemId")
        Log.d(TAG, "RESTORATION_FLOW: State - isFragmentSetupInProgress=$isFragmentSetupInProgress")
        Log.d(TAG, "RESTORATION_FLOW: State - dynamicNavigationManager.isInitialized()=${dynamicNavigationManager.isInitialized()}")

        // If dynamic navigation manager is initialized, let it handle the restoration
        if (dynamicNavigationManager.isInitialized()) {
            val currentState = dynamicNavigationManager.getCurrentState()
            Log.d(TAG, "RESTORATION_FLOW: DynamicNavigationManager currentState=$currentState")

            if (currentState != null) {
                Log.d(TAG, "RESTORATION_FLOW: Using dynamic navigation manager for state restoration")

                // Check if we have a valid currentSelectedItemId from savedInstanceState
                if (currentSelectedItemId != -1) {
                    val fragmentName = when (currentSelectedItemId) {
                        R.id.animationGridFragment -> "Animation"
                        R.id.chargeFragment -> "Charge"
                        R.id.dischargeFragment -> "Discharge"
                        R.id.healthFragment -> "Health"
                        R.id.settingsFragment -> "Settings"
                        else -> "Unknown($currentSelectedItemId)"
                    }
                    Log.d(TAG, "RESTORATION_FLOW: Attempting to restore fragment: $fragmentName (id=$currentSelectedItemId)")

                    // Check if the saved fragment conflicts with current battery state
                    if (currentSelectedItemId == R.id.chargeFragment || currentSelectedItemId == R.id.dischargeFragment) {
                        Log.d(TAG, "RESTORATION_FLOW: Contextual fragment detected - checking battery state compatibility")
                        val currentBatteryState = currentState.isCharging
                        val savedFragmentIsCharge = currentSelectedItemId == R.id.chargeFragment

                        if (currentBatteryState != savedFragmentIsCharge) {
                            Log.d(TAG, "RESTORATION_FLOW: Saved fragment ($fragmentName) conflicts with current battery state (charging=$currentBatteryState)")
                            Log.d(TAG, "RESTORATION_FLOW: Forcing navigation state reset to match current battery state")
                            dynamicNavigationManager.forceResetNavigationState()
                            return
                        }
                    }
                }

                // The dynamic navigation manager will handle fragment restoration based on charging state
                // Only restore non-contextual fragments manually
                if (currentSelectedItemId == R.id.healthFragment ||
                    currentSelectedItemId == R.id.settingsFragment ||
                    currentSelectedItemId == R.id.animationGridFragment) {
                    Log.d(TAG, "RESTORATION_FLOW: Non-contextual fragment detected, manually restoring: $currentSelectedItemId")
                    dynamicNavigationManager.handleUserNavigation(currentSelectedItemId)
                } else {
                    Log.d(TAG, "RESTORATION_FLOW: Contextual fragment or no valid ID, letting DynamicNavigationManager handle restoration")
                    // Force a battery state check to ensure proper fragment is shown
                    dynamicNavigationManager.handleBatteryStateChange()
                }
                return
            } else {
                Log.w(TAG, "RESTORATION_FLOW: DynamicNavigationManager initialized but currentState is null")
            }
        } else {
            Log.w(TAG, "RESTORATION_FLOW: DynamicNavigationManager not initialized yet")
        }

        // Fallback manual restoration if DynamicNavigationManager is not available
        Log.d(TAG, "RESTORATION_FLOW: Attempting fallback manual restoration")

        // Only restore if we have a valid non-contextual fragment and initial fragment is set
        if (isInitialFragmentSet && (currentSelectedItemId == R.id.healthFragment ||
                                    currentSelectedItemId == R.id.settingsFragment ||
                                    currentSelectedItemId == R.id.animationGridFragment)) {
            Log.d(TAG, "RESTORATION_FLOW: Fallback manual restoration for non-contextual fragment: $currentSelectedItemId")

            val fragment = when (currentSelectedItemId) {
                R.id.healthFragment -> HealthFragment()
                R.id.settingsFragment -> SettingsFragment()
                R.id.animationGridFragment -> AnimationGridFragment()
                else -> {
                    Log.w(TAG, "RESTORATION_FLOW: Unexpected fragment ID in manual restoration: $currentSelectedItemId")
                    return
                }
            }

            binding.bottomView.selectedItemId = currentSelectedItemId
            Log.d(TAG, "RESTORATION_FLOW: Manual fragment state restoration to: ${fragment.javaClass.simpleName}")
            supportFragmentManager
                .beginTransaction()
                .replace(binding.navHostFragment.id, fragment)
                .commit()
        } else {
            Log.d(TAG, "RESTORATION_FLOW: Skipping manual fragment restoration")
            Log.d(TAG, "RESTORATION_FLOW: Reason - isInitialFragmentSet=$isInitialFragmentSet, currentSelectedItemId=$currentSelectedItemId")
            Log.d(TAG, "RESTORATION_FLOW: Will be handled by dynamic navigation manager when it initializes")
        }

        Log.d(TAG, "RESTORATION_FLOW: restoreFragmentState() completed")
    }

    override fun onStart() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "RESTORATION_FLOW: onStart() called")
        super.onStart()
        Log.d(TAG, "RESTORATION_FLOW: onStart() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    override fun onResume() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "RESTORATION_FLOW: onResume() started - currentSelectedItemId=$currentSelectedItemId")
        Log.d(TAG, "RESTORATION_FLOW: isInitialFragmentSet=$isInitialFragmentSet, isFragmentSetupInProgress=$isFragmentSetupInProgress")

        super.onResume()
        Log.d(TAG, "RESTORATION_FLOW: onResume() super completed, starting fragment state restoration")

        // Apply any necessary device-specific resume handling
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                // Handle any Xiaomi-specific issues when resuming
                Log.d(TAG, "Applying Xiaomi-specific resume handling")

                // Register the animation fix receiver
                val intentFilter = IntentFilter().apply {
                    addAction("android.intent.action.SCREEN_ON")
                    addAction("android.intent.action.USER_PRESENT")
                }
                registerReceiver(animationFixReceiver, intentFilter)

                // Re-apply layout settings for MIUI compatibility
                binding.root.requestLayout()
                binding.navHostFragment.requestLayout()
            } catch (e: Exception) {
                Log.e(TAG, "Error in Xiaomi resume handling", e)
            }
        }

        // Restore fragment state when activity resumes
        restoreFragmentState()

        // DEPRECATED: Legacy BatteryMonitorService startup disabled
        // Using CoreBatteryStatsService for unified battery monitoring instead
        // Multiple battery services were causing resource waste and data inconsistency
        // if (!isServiceRunning(BatteryMonitorService::class.java)) {
        //     Log.d(TAG, "Starting BatteryMonitorService")
        //     val monitorIntent = Intent(this, BatteryMonitorService::class.java)
        //     try {
        //         ContextCompat.startForegroundService(this, monitorIntent)
        //         Log.d(TAG, "BatteryMonitorService started successfully")
        //     } catch (e: Exception) {
        //         Log.e(TAG, "Error starting BatteryMonitorService", e)
        //     }
        // }
        Log.d(TAG, "Using CoreBatteryStatsService for unified battery monitoring")

        // DEPRECATED: Legacy NewChargeMonitorService replaced by UnifiedBatteryNotificationService
        // Using unified notification service that consumes CoreBatteryStatsProvider
        // Log.d(TAG, "Starting NewChargeMonitorService via helper")
        // chargeMonitorServiceHelper.startService()

        // Start the unified battery notification service
        Log.d(TAG, "Starting UnifiedBatteryNotificationService via helper")
        unifiedBatteryNotificationServiceHelper.startService()

        // Defer non-critical service startup to background thread
        startNonCriticalServicesAsync()

        Log.d(TAG, "STARTUP_TIMING: MainActivity.onResume() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Start non-critical services asynchronously to avoid blocking UI
     */
    private fun startNonCriticalServicesAsync() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Enhanced discharge timer service
                val currentSession = dischargeSessionRepository.currentSession.value
                if (currentSession != null && currentSession.isActive && !statsChargeRepository.statsChargeStatusFlow.first().isCharging) {
                    Log.d(TAG, "Starting EnhancedDischargeTimerService via helper")
                    enhancedDischargeTimerServiceHelper.startService()
                }

                // Charging overlay service
                withContext(Dispatchers.Main) {
                    val overlayServiceStartTime = System.currentTimeMillis()
                    if (!isServiceRunning(ChargingOverlayService::class.java)) {
                        Log.d(TAG, "Starting ChargingOverlayService")
                        val overlayIntent = Intent(this@MainActivity, ChargingOverlayService::class.java)
                        ContextCompat.startForegroundService(this@MainActivity, overlayIntent)
                    }
                    Log.d(TAG, "STARTUP_TIMING: Async overlay service check took ${System.currentTimeMillis() - overlayServiceStartTime}ms")
                }

                // Anti-thief check
                withContext(Dispatchers.Main) {
                    val antiThiefStartTime = System.currentTimeMillis()
                    if (appRepository.isAntiThiefAlertActive() && !AntiThiefUtils.isEnterPasswordActivityRunning(this@MainActivity)) {
                        Log.d(TAG, "Anti-theft alert is active, launching EnterPasswordActivity")
                        val intent = Intent(this@MainActivity, com.tqhit.battery.one.activity.password.EnterPasswordActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                    }
                    Log.d(TAG, "STARTUP_TIMING: Async anti-thief check took ${System.currentTimeMillis() - antiThiefStartTime}ms")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in non-critical service startup", e)
            }
        }
    }

    override fun onPause() {
        // Unregister the animation fix receiver if it was registered
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                unregisterReceiver(animationFixReceiver)
            } catch (e: Exception) {
                Log.e(TAG, "Error unregistering animation fix receiver", e)
            }
        }

        super.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy called")
//        if (binding.bannerContainer.isEnabled)
//            applovinBannerAdManager.destroy(binding.bannerContainer)
    }

    private fun initBannerAd() {
//        if (remoteConfigHelper.getBoolean("bn_enable")) {
//            binding.bannerContainer.visibility = View.VISIBLE
//            applovinBannerAdManager.loadAd(
//                "default_bn",
//                binding.bannerContainer,
//                object : MaxAdViewAdListener {
//                    override fun onAdLoaded(p0: MaxAd) {
//                    }
//
//                    override fun onAdDisplayed(p0: MaxAd) {
//                    }
//
//                    override fun onAdHidden(p0: MaxAd) {
//                    }
//
//                    override fun onAdClicked(p0: MaxAd) {
//                    }
//
//                    override fun onAdLoadFailed(p0: String, p1: MaxError) {
//                        handler.postDelayed({ initBannerAd() }, 5000)
//                    }
//
//                    override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
//                    }
//
//                    override fun onAdExpanded(p0: MaxAd) {
//                    }
//
//                    override fun onAdCollapsed(p0: MaxAd) {
//                    }
//                }
//            )
//        } else {
//            binding.bannerContainer.visibility = View.GONE
//        }
    }

    /**
     * Apply specific handling for Xiaomi devices to prevent crashes
     */
    private fun applyXiaomiSpecificHandling() {
        if (DeviceUtils.isXiaomiDevice()) {
            try {
                Log.d(TAG, "Applying Xiaomi-specific handling for MainActivity")

                // Set window flags to prevent animation conflicts
                window?.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )

                // Use specific drawing cache settings for better MIUI compatibility
                binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                binding.navHostFragment.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
            } catch (e: Exception) {
                Log.e(TAG, "Error applying Xiaomi handling", e)
            }
        }
    }
}
