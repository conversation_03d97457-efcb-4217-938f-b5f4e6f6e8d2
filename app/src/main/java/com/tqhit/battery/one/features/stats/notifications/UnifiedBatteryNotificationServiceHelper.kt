package com.tqhit.battery.one.features.stats.notifications

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the UnifiedBatteryNotificationService.
 * Provides centralized control for starting and stopping the unified notification service.
 */
@Singleton
class UnifiedBatteryNotificationServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "UnifiedBatteryNotificationHelper"
    }
    
    /**
     * Checks if the UnifiedBatteryNotificationService is currently running.
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val isRunning = activityManager.getRunningServices(Integer.MAX_VALUE)
            .any { it.service.className == UnifiedBatteryNotificationService::class.java.name }
        
        Log.d(TAG, "isServiceRunning: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the UnifiedBatteryNotificationService.
     * Uses foreground service start for Android O+ to comply with background service restrictions.
     */
    fun startService() {
        Log.d(TAG, "Starting UnifiedBatteryNotificationService")
        
        try {
            val intent = Intent(context, UnifiedBatteryNotificationService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Use startForegroundService for Android O+ to avoid background service restrictions
                ContextCompat.startForegroundService(context, intent)
                Log.d(TAG, "Started UnifiedBatteryNotificationService as foreground service")
            } else {
                context.startService(intent)
                Log.d(TAG, "Started UnifiedBatteryNotificationService as regular service")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start UnifiedBatteryNotificationService", e)
        }
    }
    
    /**
     * Stops the UnifiedBatteryNotificationService.
     */
    fun stopService() {
        Log.d(TAG, "Stopping UnifiedBatteryNotificationService")
        
        try {
            val intent = Intent(context, UnifiedBatteryNotificationService::class.java)
            context.stopService(intent)
            Log.d(TAG, "UnifiedBatteryNotificationService stop request sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop UnifiedBatteryNotificationService", e)
        }
    }
}
