package com.tqhit.battery.one.service

/**
 * DEPRECATED: This service is kept for reference only and should not be used.
 *
 * This legacy battery monitoring service has been replaced by CoreBatteryStatsService
 * to provide unified battery data collection across the application.
 *
 * ISSUES WITH THIS SERVICE:
 * - Creates duplicate battery monitoring alongside CoreBatteryStatsService
 * - Causes resource waste with multiple battery monitoring services running
 * - Leads to data inconsistency between different battery data sources
 * - Violates single source of truth principle
 *
 * MIGRATION PATH:
 * - Use CoreBatteryStatsProvider.coreBatteryStatusFlow for battery data
 * - Consume CoreBatteryStatus data model for consistent battery information
 * - Remove service startup from MainActivity and other initialization code
 *
 * This file is preserved for reference during the migration process and will be
 * removed once all dependent code has been updated to use CoreBatteryStatsService.
 */

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.BatteryManager
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.repository.BatteryRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import java.util.*
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.AntiThiefUtils

@AndroidEntryPoint
class BatteryMonitorService : Service() {
    @Inject
    lateinit var batteryRepository: BatteryRepository
    @Inject
    lateinit var appRepository: AppRepository

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var isMonitoring = false
    private var lastBatteryLevel = -1
    private var hasNotifiedTargetPercent = false
    private var hasNotifiedFullCharge = false
    private var hasNotifiedDischargePercent = false
    private var wasCharging = false
    private var antiThiefHandler: android.os.Handler? = null
    private var antiThiefRunnable: Runnable? = null

    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "BatteryMonitorChannel"
        private const val CHANNEL_NAME = "Battery Monitor"
        private const val ALARM_NOTIFICATION_ID = 2
        private const val DISCHARGE_ALARM_NOTIFICATION_ID = 3
        private const val CHARGE_NOTIFICATION_ID = 4
        private const val DISCHARGE_NOTIFICATION_ID = 5
    }

    override fun onCreate() {
        super.onCreate()
        batteryRepository.registerReceivers()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (!isMonitoring) {
            startMonitoring()
            // Start as foreground service if battery optimization is not ignored
            if (!batteryRepository.isIgnoringBatteryOptimizations()) {
                startForeground(NOTIFICATION_ID, createNotification())
            }
        }
        return START_STICKY
    }

    private fun startMonitoring() {
        isMonitoring = true
        serviceScope.launch {
            var lastUpdateNotificationTime = 0L
            while (isMonitoring) {
                val batteryStatus = batteryRepository.getBatteryStatus()
                batteryStatus?.let {
                    batteryRepository.updateBatteryInfo(it)
                    batteryRepository.updateSessionChargeInfo(it)
                    checkChargeAlarmConditions(it)
                    checkChargingStateChange(it)
                    if (System.currentTimeMillis() - lastUpdateNotificationTime > BatteryRepository.UPDATE_NOTIFICATION_INTERVAL) {
                        updateNotification()
                        lastUpdateNotificationTime = System.currentTimeMillis()
                    }
                }
                delay(BatteryRepository.CHARGING_CHECK_INTERVAL)
            }
        }
    }

    private fun checkChargeAlarmConditions(batteryStatus: Intent) {
        val level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        val batteryPct = level * 100 / scale.toFloat()
        
        if (batteryPct.toInt() != lastBatteryLevel) {
            // Reset notification flags when battery level changes
            if (batteryPct.toInt() < lastBatteryLevel) {
                hasNotifiedTargetPercent = false
                hasNotifiedFullCharge = false
                hasNotifiedDischargePercent = false
            }
            
            // Check for target percent notification with DND check
            if (batteryRepository.isCharging.value && appRepository.getChargeAlarmPercent() > 0 &&
                batteryPct.toInt() >= appRepository.getChargeAlarmPercent() &&
                !hasNotifiedTargetPercent &&
                !isChargingInDoNotDisturbTime()) {
                showChargeAlarmNotification()
                hasNotifiedTargetPercent = true
            }
            
            // Check for full charge notification (without DND check)
            if (batteryRepository.isCharging.value && batteryPct.toInt() >= 100 &&
                !hasNotifiedFullCharge) {
                showFullChargeNotification()
                hasNotifiedFullCharge = true
            }

            // Check for discharge percent notification with DND check
            if (!batteryRepository.isCharging.value && appRepository.isDischargeAlarmEnabled() &&
                batteryPct.toInt() <= appRepository.getDischargeAlarmPercent() &&
                !hasNotifiedDischargePercent &&
                !isDischargingInDoNotDisturbTime()) {
                showDischargeAlarmNotification()
                hasNotifiedDischargePercent = true
            }
            
            lastBatteryLevel = batteryPct.toInt()
        }
    }

    private fun checkChargingStateChange(batteryStatus: Intent) {
        val isCharging = batteryRepository.isCharging.value
        if (isCharging != wasCharging) {
            if (isCharging && appRepository.isChargeNotificationEnabled()) {
                showChargeNotification()
            } else if (!isCharging && appRepository.isDischargeNotificationEnabled()) {
                showDischargeNotification()
            }
            checkAntiThiefTrigger()
            wasCharging = isCharging
        }
    }

    private fun checkAntiThiefTrigger() {
        val isCharging = batteryRepository.isCharging.value
        val antiThiefEnabled = appRepository.isAntiThiefEnabled()
        val antiThiefPasswordSet = appRepository.isAntiThiefPasswordSet()
        // Only trigger alert on transition from charging to discharging
        if (!isCharging && wasCharging && antiThiefEnabled && antiThiefPasswordSet) {
            appRepository.setAntiThiefAlertActive(true)
            antiThiefHandler = android.os.Handler(mainLooper)
            antiThiefRunnable = Runnable {
                if (!AntiThiefUtils.isEnterPasswordActivityRunning(this))
                    launchEnterPasswordActivity()
            }
            antiThiefHandler?.postDelayed(antiThiefRunnable!!, 5000)
        } else if (isCharging || !antiThiefEnabled || !antiThiefPasswordSet) {
            appRepository.setAntiThiefAlertActive(false)
            antiThiefHandler?.removeCallbacksAndMessages(null)
        }
    }

    private fun launchEnterPasswordActivity() {
        val intent = Intent(this, com.tqhit.battery.one.activity.password.EnterPasswordActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
    }

    private fun isChargingInDoNotDisturbTime(): Boolean {
        if (!appRepository.isDontDisturbChargeEnabled()) {
            return false
        }

        val currentTime = Calendar.getInstance()
        val currentHour = currentTime.get(Calendar.HOUR_OF_DAY)
        val currentMinute = currentTime.get(Calendar.MINUTE)
        val currentTimeInMinutes = currentHour * 60 + currentMinute

        // Parse DND start time
        val startTimeParts = appRepository.getDontDisturbChargeFromTime().split(":")
        val startHour = startTimeParts[0].toInt()
        val startMinute = startTimeParts[1].toInt()
        val startTimeInMinutes = startHour * 60 + startMinute

        // Parse DND end time
        val endTimeParts = appRepository.getDontDisturbChargeUntilTime().split(":")
        val endHour = endTimeParts[0].toInt()
        val endMinute = endTimeParts[1].toInt()
        val endTimeInMinutes = endHour * 60 + endMinute

        // Check if current time is within DND period
        return if (startTimeInMinutes <= endTimeInMinutes) {
            // Normal case: DND period is within the same day
            currentTimeInMinutes in startTimeInMinutes..endTimeInMinutes
        } else {
            // Special case: DND period spans across midnight
            currentTimeInMinutes >= startTimeInMinutes || currentTimeInMinutes <= endTimeInMinutes
        }
    }

    private fun isDischargingInDoNotDisturbTime() : Boolean {
        if (!appRepository.isDontDisturbDischargeEnabled()) {
            return false
        }

        val currentTime = Calendar.getInstance()
        val currentHour = currentTime.get(Calendar.HOUR_OF_DAY)
        val currentMinute = currentTime.get(Calendar.MINUTE)
        val currentTimeInMinutes = currentHour * 60 + currentMinute

        // Parse DND start time
        val startTimeParts = appRepository.getDontDisturbDischargeFromTime().split(":")
        val startHour = startTimeParts[0].toInt()
        val startMinute = startTimeParts[1].toInt()
        val startTimeInMinutes = startHour * 60 + startMinute

        // Parse DND end time
        val endTimeParts = appRepository.getDontDisturbDischargeUntilTime().split(":")
        val endHour = endTimeParts[0].toInt()
        val endMinute = endTimeParts[1].toInt()
        val endTimeInMinutes = endHour * 60 + endMinute

        // Check if current time is within DND period
        return if (startTimeInMinutes <= endTimeInMinutes) {
            // Normal case: DND period is within the same day
            currentTimeInMinutes in startTimeInMinutes..endTimeInMinutes
        } else {
            // Special case: DND period spans across midnight
            currentTimeInMinutes >= startTimeInMinutes || currentTimeInMinutes <= endTimeInMinutes
        }
    }

    private fun showChargeAlarmNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.charge_reached))
            .setContentText(getString(R.string.please_disconnect_charger))
            .setSmallIcon(R.drawable.high_battery)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(ALARM_NOTIFICATION_ID, notification)

        // Add vibration if enabled
        if (appRepository.isVibrationEnabled()) {
            val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(1000, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(1000)
            }
        }
    }

    private fun showFullChargeNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(getString(R.string.fullbattery_top))
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(ALARM_NOTIFICATION_ID, notification)
    }

    private fun showDischargeAlarmNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.low_battery))
            .setContentText(getString(R.string.low_battery_text_1) + getString(R.string.low_battery_text_2))
            .setSmallIcon(R.drawable.low_battery)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(DISCHARGE_ALARM_NOTIFICATION_ID, notification)

        // Add vibration if enabled
        if (appRepository.isVibrationEnabled()) {
            val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(1000, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(1000)
            }
        }
    }

    private fun showChargeNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.charge_notification))
            .setContentText(getString(R.string.charge_notification_text))
            .setSmallIcon(R.drawable.notify_2)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(CHARGE_NOTIFICATION_ID, notification)

        if (appRepository.isVibrationEnabled() && appRepository.isVibrationChargeEnabled()) {
            vibrate()
        }
    }

    private fun showDischargeNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.discharge_notification))
            .setContentText(getString(R.string.discharge_notification_text))
            .setSmallIcon(R.drawable.notify_2)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(DISCHARGE_NOTIFICATION_ID, notification)

        if (appRepository.isVibrationEnabled() && appRepository.isVibrationDischargeEnabled()) {
            vibrate()
        }
    }

    private fun vibrate() {
        val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createOneShot(1000, VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(1000)
        }
    }

    private fun updateNotification() {
        // Get current battery metrics
        val amperage = batteryRepository.amperage.value
        val power = batteryRepository.power.value
        val chargingRate = batteryRepository.chargingRate.value
        val temperature = batteryRepository.temperature.value

        // Format the content text
        val content = "Now: ${String.format("%.1f", amperage)} mA (${String.format("%.1f", power)} W) • ${String.format("%.1f", chargingRate)}%/h • Temp: ${String.format("%.1f", temperature)}°C"

        // Update the notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(content)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            ))
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)  // Make notification persistent
            .setAutoCancel(false)  // Prevent auto-cancellation
            .build()

        // Update the notification
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Battery monitoring service"
                setShowBadge(false)
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // Get current battery metrics
        val amperage = batteryRepository.amperage.value
        val power = batteryRepository.power.value
        val chargingRate = batteryRepository.chargingRate.value
        val temperature = batteryRepository.temperature.value

        // Format the content text
        val content = "Now: ${String.format("%.1f", amperage)} mA (${String.format("%.1f", power)} W) • ${String.format("%.1f", chargingRate)}%/h • Temp: ${String.format("%.1f", temperature)}°C"

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(content)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)  // Make notification persistent
            .setAutoCancel(false)  // Prevent auto-cancellation
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        isMonitoring = false
        serviceScope.cancel()
        batteryRepository.unregisterReceivers()
        antiThiefHandler?.removeCallbacksAndMessages(null)
    }

    override fun onBind(intent: Intent?): IBinder? = null
} 