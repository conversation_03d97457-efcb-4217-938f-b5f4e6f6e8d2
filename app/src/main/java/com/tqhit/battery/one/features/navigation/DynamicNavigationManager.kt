package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages dynamic navigation based on real-time charging state.
 * This class coordinates fragment switching and navigation visibility
 * based on battery charging status from CoreBatteryStatsProvider.
 */
@Singleton
class DynamicNavigationManager @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "DynamicNavigationManager"
    }
    
    private val _navigationState = MutableStateFlow<NavigationState?>(null)
    val navigationState: StateFlow<NavigationState?> = _navigationState.asStateFlow()
    
    private val _stateChanges = MutableStateFlow<NavigationStateChange?>(null)
    val stateChanges: StateFlow<NavigationStateChange?> = _stateChanges.asStateFlow()
    
    private var isInitialized = false
    private var fragmentManager: FragmentManager? = null
    private var bottomNavigationView: BottomNavigationView? = null
    private var fragmentContainerId: Int = 0
    private var isUpdatingNavigation = false
    
    /**
     * Initializes the navigation manager with required components.
     * Must be called before using other methods.
     *
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner
    ) {
        if (isInitialized) {
            Log.w(TAG, "DynamicNavigationManager already initialized")
            return
        }
        
        this.fragmentManager = fragmentManager
        this.bottomNavigationView = bottomNavigationView
        this.fragmentContainerId = fragmentContainerId
        
        Log.d(TAG, "Initializing DynamicNavigationManager")
        
        // Start monitoring battery status changes
        startBatteryStatusMonitoring(lifecycleOwner)
        
        // Set up initial state
        setupInitialState()
        
        isInitialized = true
        Log.d(TAG, "DynamicNavigationManager initialized successfully")
    }
    
    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->
                    Log.d(TAG, "Battery charging state changed: $isCharging")
                    handleChargingStateChange(isCharging)
                }
        }
    }
    
    /**
     * Sets up the initial navigation state based on current battery status.
     */
    private fun setupInitialState() {
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        val isCharging = currentBatteryStatus?.isCharging ?: false
        
        Log.d(TAG, "Setting up initial state - charging: $isCharging")
        
        // val initialState = if (isCharging) {
        //     NavigationState.createChargingState(shouldShowTransition = false)
        // } else {
        //     NavigationState.createDischargingState(shouldShowTransition = false)
        // }
        val initialState = NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
        
        updateNavigationState(initialState, StateChangeReason.INITIAL_SETUP)
    }
    
    /**
     * Handles charging state changes and updates navigation accordingly.
     */
    private fun handleChargingStateChange(isCharging: Boolean) {
        val currentState = _navigationState.value
        
        // Don't update if the state hasn't actually changed
        if (currentState?.isCharging == isCharging) {
            Log.v(TAG, "Charging state unchanged, skipping update")
            return
        }
        
        val newState = if (isCharging) {
            NavigationState.createChargingState()
        } else {
            NavigationState.createDischargingState()
        }
        
        val reason = if (isCharging) StateChangeReason.CHARGING_STARTED else StateChangeReason.CHARGING_STOPPED
        updateNavigationState(newState, reason)
    }
    
    /**
     * Updates the navigation state and applies changes to UI.
     */
    private fun updateNavigationState(newState: NavigationState, reason: StateChangeReason) {
        val previousState = _navigationState.value
        _navigationState.value = newState
        
        Log.d(TAG, "Navigation state updated: ${newState.activeFragmentId} (charging: ${newState.isCharging})")
        
        // Emit state change event
        val stateChange = NavigationStateChange(previousState, newState, reason)
        _stateChanges.value = stateChange
        
        // Apply changes to UI
        applyNavigationChanges(newState, previousState)
    }
    
    /**
     * Applies navigation changes to the UI components.
     */
    private fun applyNavigationChanges(newState: NavigationState, previousState: NavigationState?) {
        try {
            // Update fragment if needed
            if (previousState?.activeFragmentId != newState.activeFragmentId) {
                switchToFragment(newState)
            }
            
            // Update bottom navigation visibility and selection
            updateBottomNavigation(newState)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying navigation changes", e)
        }
    }
    
    /**
     * Switches to the fragment specified in the navigation state.
     */
    private fun switchToFragment(state: NavigationState) {
        val fragmentManager = this.fragmentManager ?: run {
            Log.e(TAG, "FragmentManager not available for fragment switch")
            return
        }

        try {
            val fragment = state.createFragment()
            Log.d(TAG, "Switching to fragment: ${fragment.javaClass.simpleName}")

            val transaction = fragmentManager.beginTransaction()

            if (state.shouldShowTransition) {
                // Add transition animations
                transaction.setCustomAnimations(
                    android.R.anim.fade_in,
                    android.R.anim.fade_out
                )
            }

            transaction.replace(fragmentContainerId, fragment)

            // Use commitAllowingStateLoss to prevent IllegalStateException when app is in background
            transaction.commitAllowingStateLoss()

        } catch (e: Exception) {
            Log.e(TAG, "Error switching fragment", e)
        }
    }
    
    /**
     * Updates the bottom navigation view based on the navigation state.
     */
    private fun updateBottomNavigation(state: NavigationState) {
        val bottomNav = this.bottomNavigationView ?: run {
            Log.e(TAG, "BottomNavigationView not available")
            return
        }

        try {
            // Prevent infinite loop by setting flag
            isUpdatingNavigation = true

            // Update selected item
            bottomNav.selectedItemId = state.activeFragmentId

            // Update menu item visibility
            val menu = bottomNav.menu
            for (i in 0 until menu.size()) {
                val menuItem = menu.getItem(i)
                val shouldBeVisible = state.isMenuItemVisible(menuItem.itemId)
                menuItem.isVisible = shouldBeVisible

                Log.v(TAG, "Menu item ${menuItem.itemId} visibility: $shouldBeVisible")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error updating bottom navigation", e)
        } finally {
            // Reset flag
            isUpdatingNavigation = false
        }
    }
    
    /**
     * Handles user navigation selection.
     * Returns true if the navigation was handled, false otherwise.
     */
    fun handleUserNavigation(itemId: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "Navigation manager not initialized")
            return false
        }

        // Prevent infinite loop - ignore navigation events triggered by our own updates
        if (isUpdatingNavigation) {
            Log.v(TAG, "Ignoring navigation event during update")
            return true
        }

        val currentState = _navigationState.value ?: return false

        // Check if the selected item is visible in current state
        if (!currentState.isMenuItemVisible(itemId)) {
            Log.w(TAG, "User tried to navigate to hidden item: $itemId")
            return false
        }

        // Don't update if already on the same fragment
        if (currentState.activeFragmentId == itemId) {
            Log.v(TAG, "Already on fragment $itemId, skipping update")
            return true
        }

        // Create new state for user navigation
        val newState = currentState.copy(
            activeFragmentId = itemId,
            shouldShowTransition = true
        )

        updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
        return true
    }
    
    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = _navigationState.value
    
    /**
     * Checks if the navigation manager is initialized.
     */
    fun isInitialized(): Boolean = isInitialized
}
