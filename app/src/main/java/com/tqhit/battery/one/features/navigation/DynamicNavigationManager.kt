package com.tqhit.battery.one.features.navigation

import android.content.Context
import android.os.BatteryManager
import android.util.Log
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages dynamic navigation based on real-time charging state.
 * This class coordinates fragment switching and navigation visibility
 * based on battery charging status from CoreBatteryStatsProvider.
 */
@Singleton
class DynamicNavigationManager @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "DynamicNavigationManager"
    }
    
    private val _navigationState = MutableStateFlow<NavigationState?>(null)
    val navigationState: StateFlow<NavigationState?> = _navigationState.asStateFlow()
    
    private val _stateChanges = MutableStateFlow<NavigationStateChange?>(null)
    val stateChanges: StateFlow<NavigationStateChange?> = _stateChanges.asStateFlow()
    
    private var isInitialized = false
    private var fragmentManager: FragmentManager? = null
    private var bottomNavigationView: BottomNavigationView? = null
    private var fragmentContainerId: Int = 0
    private var isUpdatingNavigation = false
    
    /**
     * Initializes the navigation manager with required components.
     * Must be called before using other methods.
     *
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner
    ) {
        Log.d(TAG, "RESTORATION_FLOW: initialize() called")
        Log.d(TAG, "RESTORATION_FLOW: isInitialized=$isInitialized")

        if (isInitialized) {
            Log.w(TAG, "RESTORATION_FLOW: DynamicNavigationManager already initialized - forcing re-initialization to ensure proper state")
            // Force re-initialization to ensure proper state during restoration
            // This handles cases where the manager was initialized but state was corrupted
            isInitialized = false
            _navigationState.value = null
        }

        this.fragmentManager = fragmentManager
        this.bottomNavigationView = bottomNavigationView
        this.fragmentContainerId = fragmentContainerId

        Log.d(TAG, "RESTORATION_FLOW: Initializing DynamicNavigationManager")
        Log.d(TAG, "RESTORATION_FLOW: fragmentContainerId=$fragmentContainerId")
        Log.d(TAG, "RESTORATION_FLOW: bottomNavigationView menu size=${bottomNavigationView.menu.size()}")

        // Log current menu state before initialization
        val menu = bottomNavigationView.menu
        for (i in 0 until menu.size()) {
            val menuItem = menu.getItem(i)
            Log.d(TAG, "RESTORATION_FLOW: Initial menu item ${getMenuItemName(menuItem.itemId)} (${menuItem.itemId}) visible=${menuItem.isVisible}")
        }

        // Inflate menu with proper filtering based on initial battery state
        Log.d(TAG, "RESTORATION_FLOW: Inflating filtered menu")
        inflateFilteredMenu()

        // Start monitoring battery status changes
        Log.d(TAG, "RESTORATION_FLOW: Starting battery status monitoring")
        startBatteryStatusMonitoring(lifecycleOwner)

        // Set up initial state
        Log.d(TAG, "RESTORATION_FLOW: Setting up initial state")
        setupInitialState()

        isInitialized = true
        Log.d(TAG, "RESTORATION_FLOW: DynamicNavigationManager initialized successfully")
    }
    
    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->
                    Log.d(TAG, "Battery charging state changed: $isCharging")
                    handleChargingStateChange(isCharging)
                }
        }
    }
    
    /**
     * Sets up the initial navigation state based on current battery status.
     */
    fun setupInitialState() {
        Log.d(TAG, "RESTORATION_FLOW: setupInitialState() called")

        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        Log.d(TAG, "RESTORATION_FLOW: currentBatteryStatus=$currentBatteryStatus")

        // If no battery status is available, try to get it from system directly
        val isCharging = if (currentBatteryStatus != null) {
            Log.d(TAG, "RESTORATION_FLOW: Using CoreBatteryStatsProvider status: isCharging=${currentBatteryStatus.isCharging}")
            currentBatteryStatus.isCharging
        } else {
            Log.w(TAG, "RESTORATION_FLOW: CoreBatteryStatsProvider has no status, falling back to system battery check")
            // Fallback to direct system battery check
            try {
                val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as? BatteryManager
                val isChargingFallback = batteryManager?.isCharging ?: false
                Log.d(TAG, "RESTORATION_FLOW: System battery manager charging state: $isChargingFallback")
                isChargingFallback
            } catch (e: Exception) {
                Log.e(TAG, "RESTORATION_FLOW: Error getting system battery status, defaulting to false", e)
                false
            }
        }

        Log.d(TAG, "RESTORATION_FLOW: Final charging state determination: $isCharging")
        Log.d(TAG, "RESTORATION_FLOW: Battery status available: ${currentBatteryStatus != null}")

        // Create proper initial state based on charging status
        // This ensures only the contextual charge/discharge fragment is visible
        val initialState = if (isCharging) {
            Log.d(TAG, "RESTORATION_FLOW: Creating charging state - showing charge fragment only")
            NavigationState.createChargingState(shouldShowTransition = false)
        } else {
            Log.d(TAG, "RESTORATION_FLOW: Creating discharging state - showing discharge fragment only")
            NavigationState.createDischargingState(shouldShowTransition = false)
        }

        Log.d(TAG, "RESTORATION_FLOW: Initial state created: activeFragmentId=${initialState.activeFragmentId}, isCharging=${initialState.isCharging}")
        Log.d(TAG, "RESTORATION_FLOW: Initial state visible menu items: ${initialState.visibleMenuItems}")

        updateNavigationState(initialState, StateChangeReason.INITIAL_SETUP)

        // Force immediate menu visibility update to ensure proper state during restoration
        Log.d(TAG, "RESTORATION_FLOW: Forcing immediate menu visibility update")
        updateBottomNavigation(initialState)

        Log.d(TAG, "RESTORATION_FLOW: setupInitialState() completed")
    }
    
    /**
     * Handles charging state changes and updates navigation accordingly.
     */
    private fun handleChargingStateChange(isCharging: Boolean) {
        Log.d(TAG, "RESTORATION_FLOW: handleChargingStateChange() called with isCharging=$isCharging")

        val currentState = _navigationState.value
        Log.d(TAG, "RESTORATION_FLOW: currentState=${currentState?.activeFragmentId} (charging: ${currentState?.isCharging})")

        // Don't update if the state hasn't actually changed
        if (currentState?.isCharging == isCharging) {
            Log.d(TAG, "RESTORATION_FLOW: Charging state unchanged ($isCharging), skipping update")
            return
        }

        Log.d(TAG, "RESTORATION_FLOW: Charging state changed from ${currentState?.isCharging} to $isCharging, updating navigation")

        val newState = if (isCharging) {
            Log.d(TAG, "RESTORATION_FLOW: Switching to charging state - showing charge fragment only")
            NavigationState.createChargingState()
        } else {
            Log.d(TAG, "RESTORATION_FLOW: Switching to discharging state - showing discharge fragment only")
            NavigationState.createDischargingState()
        }

        Log.d(TAG, "RESTORATION_FLOW: New state created - activeFragmentId=${newState.activeFragmentId}, visibleMenuItems=${newState.visibleMenuItems}")

        val reason = if (isCharging) StateChangeReason.CHARGING_STARTED else StateChangeReason.CHARGING_STOPPED
        Log.d(TAG, "RESTORATION_FLOW: Updating navigation state with reason=$reason")
        updateNavigationState(newState, reason)
    }
    
    /**
     * Updates the navigation state and applies changes to UI.
     */
    private fun updateNavigationState(newState: NavigationState, reason: StateChangeReason) {
        Log.d(TAG, "RESTORATION_FLOW: updateNavigationState() called")
        Log.d(TAG, "RESTORATION_FLOW: reason=$reason")

        val previousState = _navigationState.value
        Log.d(TAG, "RESTORATION_FLOW: previousState=${previousState?.activeFragmentId} (charging: ${previousState?.isCharging})")
        Log.d(TAG, "RESTORATION_FLOW: newState=${newState.activeFragmentId} (charging: ${newState.isCharging})")
        Log.d(TAG, "RESTORATION_FLOW: newState visible menu items: ${newState.visibleMenuItems}")

        _navigationState.value = newState

        // Emit state change event
        val stateChange = NavigationStateChange(previousState, newState, reason)
        _stateChanges.value = stateChange
        Log.d(TAG, "RESTORATION_FLOW: State change event emitted")

        // Apply changes to UI
        Log.d(TAG, "RESTORATION_FLOW: Applying navigation changes to UI")
        applyNavigationChanges(newState, previousState)

        // Ensure menu visibility is properly enforced after state change
        enforceMenuVisibility(newState)

        Log.d(TAG, "RESTORATION_FLOW: updateNavigationState() completed")
    }
    
    /**
     * Applies navigation changes to the UI components.
     */
    private fun applyNavigationChanges(newState: NavigationState, previousState: NavigationState?) {
        try {
            // Update fragment if needed
            if (previousState?.activeFragmentId != newState.activeFragmentId) {
                switchToFragment(newState)
            }
            
            // Update bottom navigation visibility and selection
            updateBottomNavigation(newState)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying navigation changes", e)
        }
    }
    
    /**
     * Switches to the fragment specified in the navigation state.
     */
    private fun switchToFragment(state: NavigationState) {
        val fragmentManager = this.fragmentManager ?: run {
            Log.e(TAG, "FragmentManager not available for fragment switch")
            return
        }

        try {
            val fragment = state.createFragment()
            Log.d(TAG, "Switching to fragment: ${fragment.javaClass.simpleName}")

            val transaction = fragmentManager.beginTransaction()

            if (state.shouldShowTransition) {
                // Add transition animations
                transaction.setCustomAnimations(
                    android.R.anim.fade_in,
                    android.R.anim.fade_out
                )
            }

            transaction.replace(fragmentContainerId, fragment)

            // Use commitAllowingStateLoss to prevent IllegalStateException when app is in background
            transaction.commitAllowingStateLoss()

        } catch (e: Exception) {
            Log.e(TAG, "Error switching fragment", e)
        }
    }
    
    /**
     * Updates the bottom navigation view based on the navigation state.
     * Uses menu item removal/addition instead of visibility to ensure proper layout.
     */
    private fun updateBottomNavigation(state: NavigationState) {
        Log.d(TAG, "RESTORATION_FLOW: updateBottomNavigation() called")

        val bottomNav = this.bottomNavigationView ?: run {
            Log.e(TAG, "RESTORATION_FLOW: BottomNavigationView not available")
            return
        }

        try {
            // Prevent infinite loop by setting flag
            isUpdatingNavigation = true
            Log.d(TAG, "RESTORATION_FLOW: Set isUpdatingNavigation=true")

            Log.d(TAG, "RESTORATION_FLOW: Updating bottom navigation - active fragment: ${state.activeFragmentId}, charging: ${state.isCharging}")
            Log.d(TAG, "RESTORATION_FLOW: Target visible menu items: ${state.visibleMenuItems}")

            // Log current state before changes
            val menu = bottomNav.menu
            Log.d(TAG, "RESTORATION_FLOW: Current selectedItemId: ${bottomNav.selectedItemId}")
            Log.d(TAG, "RESTORATION_FLOW: Current menu size: ${menu.size()}")

            // Rebuild menu to ensure proper visibility
            rebuildNavigationMenu(bottomNav, state)

            // Update selected item
            Log.d(TAG, "RESTORATION_FLOW: Setting selectedItemId to ${state.activeFragmentId}")
            bottomNav.selectedItemId = state.activeFragmentId

            Log.d(TAG, "RESTORATION_FLOW: Final selectedItemId: ${bottomNav.selectedItemId}")
            Log.d(TAG, "RESTORATION_FLOW: Final menu size: ${bottomNav.menu.size()}")

        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error updating bottom navigation", e)
        } finally {
            // Reset flag
            isUpdatingNavigation = false
            Log.d(TAG, "RESTORATION_FLOW: Set isUpdatingNavigation=false")
            Log.d(TAG, "RESTORATION_FLOW: updateBottomNavigation() completed")
        }
    }


    
    /**
     * Handles user navigation selection.
     * Returns true if the navigation was handled, false otherwise.
     */
    fun handleUserNavigation(itemId: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "Navigation manager not initialized")
            return false
        }

        // Prevent infinite loop - ignore navigation events triggered by our own updates
        if (isUpdatingNavigation) {
            Log.v(TAG, "Ignoring navigation event during update")
            return true
        }

        val currentState = _navigationState.value ?: return false

        // Check if the selected item is visible in current state
        if (!currentState.isMenuItemVisible(itemId)) {
            Log.w(TAG, "User tried to navigate to hidden item: $itemId")
            return false
        }

        // Don't update if already on the same fragment
        if (currentState.activeFragmentId == itemId) {
            Log.v(TAG, "Already on fragment $itemId, skipping update")
            return true
        }

        // Create new state for user navigation
        val newState = currentState.copy(
            activeFragmentId = itemId,
            shouldShowTransition = true
        )

        updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
        return true
    }
    
    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = _navigationState.value
    
    /**
     * Checks if the navigation manager is initialized.
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * Forces a battery state check and navigation update.
     * Useful for restoration scenarios.
     */
    fun handleBatteryStateChange() {
        Log.d(TAG, "RESTORATION_FLOW: handleBatteryStateChange() called")

        if (!isInitialized) {
            Log.w(TAG, "RESTORATION_FLOW: Navigation manager not initialized, cannot handle battery state change")
            return
        }

        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        val isCharging = if (currentBatteryStatus != null) {
            Log.d(TAG, "RESTORATION_FLOW: Using CoreBatteryStatsProvider status: isCharging=${currentBatteryStatus.isCharging}")
            currentBatteryStatus.isCharging
        } else {
            Log.w(TAG, "RESTORATION_FLOW: CoreBatteryStatsProvider has no status, falling back to system battery check")
            try {
                val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as? BatteryManager
                val isChargingFallback = batteryManager?.isCharging ?: false
                Log.d(TAG, "RESTORATION_FLOW: System battery manager charging state: $isChargingFallback")
                isChargingFallback
            } catch (e: Exception) {
                Log.e(TAG, "RESTORATION_FLOW: Error getting system battery status, defaulting to false", e)
                false
            }
        }

        Log.d(TAG, "RESTORATION_FLOW: Forcing battery state change handling with isCharging=$isCharging")
        handleChargingStateChange(isCharging)
    }

    /**
     * Inflates the menu with proper filtering based on current battery state.
     * This is called during initialization to ensure only appropriate items are added.
     */
    private fun inflateFilteredMenu() {
        val bottomNav = this.bottomNavigationView ?: return

        Log.d(TAG, "RESTORATION_FLOW: inflateFilteredMenu() called")

        try {
            val menu = bottomNav.menu

            // Clear any existing menu items
            menu.clear()
            Log.d(TAG, "RESTORATION_FLOW: Cleared existing menu items")

            // Determine current battery state for filtering
            val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
            val isCharging = if (currentBatteryStatus != null) {
                currentBatteryStatus.isCharging
            } else {
                // Fallback to system battery check
                try {
                    val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as? BatteryManager
                    batteryManager?.isCharging ?: false
                } catch (e: Exception) {
                    Log.e(TAG, "RESTORATION_FLOW: Error getting battery status for menu filtering", e)
                    false
                }
            }

            Log.d(TAG, "RESTORATION_FLOW: Filtering menu based on charging state: $isCharging")

            // Define all possible menu items with their properties
            val allMenuItems = listOf(
                MenuItemData(R.id.animationGridFragment, "Animation", R.drawable.ic_animation_icon),
                MenuItemData(R.id.chargeFragment, "Charge", R.drawable.ic_charge_icon),
                MenuItemData(R.id.dischargeFragment, "Discharge", R.drawable.ic_discharge_icon),
                MenuItemData(R.id.healthFragment, "Health", R.drawable.ic_health_icon),
                MenuItemData(R.id.settingsFragment, "Settings", R.drawable.ic_settings_icon)
            )

            // Add only appropriate menu items based on battery state
            var addedCount = 0
            for (itemData in allMenuItems) {
                val shouldAdd = when (itemData.id) {
                    R.id.chargeFragment -> isCharging
                    R.id.dischargeFragment -> !isCharging
                    else -> true // Always visible items (animation, health, settings)
                }

                if (shouldAdd) {
                    val menuItem = menu.add(0, itemData.id, addedCount, itemData.title)
                    menuItem.setIcon(itemData.iconRes)
                    addedCount++
                    Log.d(TAG, "RESTORATION_FLOW: Added menu item: ${itemData.title} (${itemData.id})")
                } else {
                    Log.d(TAG, "RESTORATION_FLOW: Skipped menu item: ${itemData.title} (${itemData.id}) - not appropriate for current state")
                }
            }

            Log.d(TAG, "RESTORATION_FLOW: Inflated filtered menu with $addedCount items")

        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error inflating filtered menu", e)
        }
    }

    /**
     * Rebuilds the navigation menu to ensure only visible items are present.
     * Uses menu item removal instead of clearing and rebuilding for better compatibility.
     */
    private fun rebuildNavigationMenu(bottomNav: BottomNavigationView, state: NavigationState) {
        Log.d(TAG, "RESTORATION_FLOW: rebuildNavigationMenu() called")

        try {
            val menu = bottomNav.menu
            Log.d(TAG, "RESTORATION_FLOW: Current menu size before rebuild: ${menu.size()}")

            // Clear and rebuild with filtered items
            menu.clear()
            Log.d(TAG, "RESTORATION_FLOW: Cleared menu for rebuild")

            // Define all possible menu items with their properties
            val allMenuItems = listOf(
                MenuItemData(R.id.animationGridFragment, "Animation", R.drawable.ic_animation_icon),
                MenuItemData(R.id.chargeFragment, "Charge", R.drawable.ic_charge_icon),
                MenuItemData(R.id.dischargeFragment, "Discharge", R.drawable.ic_discharge_icon),
                MenuItemData(R.id.healthFragment, "Health", R.drawable.ic_health_icon),
                MenuItemData(R.id.settingsFragment, "Settings", R.drawable.ic_settings_icon)
            )

            // Add only visible menu items
            var addedCount = 0
            for (itemData in allMenuItems) {
                if (state.isMenuItemVisible(itemData.id)) {
                    val menuItem = menu.add(0, itemData.id, addedCount, itemData.title)
                    menuItem.setIcon(itemData.iconRes)
                    addedCount++
                    Log.d(TAG, "RESTORATION_FLOW: Added menu item: ${itemData.title} (${itemData.id})")
                } else {
                    Log.d(TAG, "RESTORATION_FLOW: Skipped menu item: ${itemData.title} (${itemData.id})")
                }
            }

            Log.d(TAG, "RESTORATION_FLOW: Rebuilt menu with $addedCount items")

        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error rebuilding navigation menu", e)
        }
    }

    /**
     * Data class for menu item information
     */
    private data class MenuItemData(
        val id: Int,
        val title: String,
        val iconRes: Int
    )

    /**
     * Enforces menu visibility based on the current navigation state.
     * This is a safety mechanism to ensure proper visibility after state changes.
     */
    private fun enforceMenuVisibility(state: NavigationState) {
        val bottomNav = this.bottomNavigationView ?: return

        Log.d(TAG, "RESTORATION_FLOW: enforceMenuVisibility() called")

        try {
            // Use menu rebuilding instead of visibility for better reliability
            rebuildNavigationMenu(bottomNav, state)

            Log.d(TAG, "RESTORATION_FLOW: Menu visibility enforced via rebuilding")

        } catch (e: Exception) {
            Log.e(TAG, "RESTORATION_FLOW: Error enforcing menu visibility", e)
        }
    }

    /**
     * Forces a complete reset and re-initialization of navigation state.
     * Useful for restoration scenarios where state might be corrupted.
     */
    fun forceResetNavigationState() {
        Log.d(TAG, "RESTORATION_FLOW: forceResetNavigationState() called")

        if (!isInitialized) {
            Log.w(TAG, "RESTORATION_FLOW: Navigation manager not initialized, cannot reset state")
            return
        }

        // Clear current state
        _navigationState.value = null

        // Re-setup initial state based on current battery status
        setupInitialState()

        Log.d(TAG, "RESTORATION_FLOW: Navigation state reset completed")
    }

    /**
     * Helper method to get readable menu item names for logging
     */
    private fun getMenuItemName(itemId: Int): String {
        return when (itemId) {
            R.id.animationGridFragment -> "Animation"
            R.id.chargeFragment -> "Charge"
            R.id.dischargeFragment -> "Discharge"
            R.id.healthFragment -> "Health"
            R.id.settingsFragment -> "Settings"
            else -> "Unknown($itemId)"
        }
    }
}
