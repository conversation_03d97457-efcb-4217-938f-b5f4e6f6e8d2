# Screen Time Gap Validation Fix

## Problem Description
The TJ_BatteryOne Stats/Discharge module had screen time tracking issues where Screen On time + Screen Off time did not equal the Total session time, with gaps exceeding the acceptable 60-second tolerance.

## Root Cause Analysis
1. **Tolerance too strict**: Previous tolerance was only 5 seconds, but requirement is 60 seconds maximum gap
2. **No dedicated background validation**: Missing 5-second validation process during Screen OFF periods
3. **Gap calculation inconsistencies**: Simplified calculation method wasn't consistently applied
4. **Missing 60-second tolerance enforcement**: System didn't properly handle the acceptable gap requirement

## Solution Implementation

### 1. Updated Tolerance to 60 Seconds
**File**: `DischargeSessionRepository.kt` - `verifyScreenTimeConsistency()` method

```kotlin
// Updated tolerance to 60 seconds for individual component differences
val tolerance = 60000L // Increased from 5 seconds to 60 seconds

// ENHANCED: Check the core requirement - Screen On + Screen Off should equal Total session time within 60 seconds
val totalTimeGap = kotlin.math.abs(uiTotalTime - sessionDuration)
val maxAcceptableGap = 60000L // 60 seconds as per requirements

if (totalTimeGap > maxAcceptableGap) {
    Log.e(TAG, "SCREEN_TIME_GAP_VIOLATION: Screen time sum deviates from session duration by ${totalTimeGap/1000}s!")
    correctScreenTimeGap(uiOnTime, uiOffTime, session)
}
```

### 2. Implemented Background Validation Process
**File**: `EnhancedDischargeTimerService.kt` - Enhanced 5-second validation loop

```kotlin
// NEW: Perform dedicated gap validation during Screen OFF periods
performGapValidation(session)

private fun performGapValidation(session: DischargeSessionData) {
    // Only perform gap validation during Screen OFF periods as per requirements
    if (!isScreenOn) {
        val gap = kotlin.math.abs(totalScreenTime - sessionDuration)
        val maxAcceptableGap = 60000L // 60 seconds
        
        if (gap > maxAcceptableGap) {
            // Apply simplified calculation: Screen OFF time = Total session time - Screen ON time
            val correctedOffTime = sessionDuration - uiOnTime
            dischargeSessionRepository.applyScreenTimeGapCorrection(correctedOffTime)
        }
    }
}
```

### 3. Enhanced Simplified Calculation Method
**File**: `ScreenStateTimeTracker.kt` - `calculateTimesSimplified()` method

```kotlin
// Screen is OFF - use enhanced simplified calculation with gap validation
val calculatedOffTime = totalSessionTime - updatedOnTime
updatedOffTime = kotlin.math.max(0L, calculatedOffTime)

// ENHANCED: Validate the gap and apply correction if needed
val currentTotalTime = updatedOnTime + updatedOffTime
val gap = kotlin.math.abs(currentTotalTime - totalSessionTime)
val maxAcceptableGap = 60000L // 60 seconds tolerance

if (gap > maxAcceptableGap) {
    // Force the corrected OFF time to ensure total matches session duration
    updatedOffTime = calculatedOffTime
    _screenOffTimeUI.value = updatedOffTime
}
```

### 4. Added Gap Correction Methods
**File**: `ScreenStateTimeTracker.kt` - New `forceSetScreenOffTime()` method

```kotlin
fun forceSetScreenOffTime(correctedOffTime: Long) {
    Log.i(TAG, "FORCE_SET_OFF_TIME: Setting Screen OFF time to ${correctedOffTime/1000}s for gap correction")
    _screenOffTimeUI.value = correctedOffTime
    lastIncrementTime = System.currentTimeMillis()
}
```

**File**: `DischargeSessionRepository.kt` - New `applyScreenTimeGapCorrection()` method

```kotlin
fun applyScreenTimeGapCorrection(correctedOffTime: Long) {
    screenStateTimeTracker.forceSetScreenOffTime(correctedOffTime)
}
```

## Key Features

### 1. 60-Second Tolerance Enforcement
- Maximum acceptable gap between Screen On + Screen Off time and Total session time is 60 seconds
- Automatic correction when gap exceeds tolerance
- Comprehensive logging for debugging

### 2. Background Validation Process
- Runs every 5 seconds during Screen OFF periods
- Uses simplified calculation: Screen OFF time = Total session time - Screen ON time
- Only applies corrections during Screen OFF periods to minimize disruption

### 3. Enhanced Mathematical Constraints
- Validates that screen time sum ≤ session duration + 60 seconds tolerance
- Prevents time going backwards
- Handles edge cases like very short sessions and rapid state changes

### 4. Comprehensive Logging
- Detailed gap tracking and validation logs
- Clear indication when corrections are applied
- Performance monitoring for debugging

## Testing Strategy

### Unit Tests
- Gap validation within 60-second tolerance
- Simplified calculation during Screen OFF periods
- Gap correction when exceeding tolerance
- Mathematical constraint validation
- Edge cases (short sessions, rapid state changes)

### ADB Testing Commands
```bash
# Monitor screen time tracking logs
adb logcat -s ScreenStateTimeTracker EnhancedDischargeTimer DischargeSessionRepository

# Test screen state changes
adb shell input keyevent KEYCODE_POWER  # Turn screen off
adb shell input keyevent KEYCODE_POWER  # Turn screen on

# Monitor gap validation
adb logcat | grep "GAP_VALIDATION\|SCREEN_TIME_GAP"
```

## Expected Results

1. **Gap within tolerance**: Screen On + Screen Off time should equal Total session time ± 60 seconds
2. **Automatic correction**: Gaps exceeding 60 seconds are automatically corrected during Screen OFF periods
3. **Consistent tracking**: Screen time calculations remain accurate during various usage patterns
4. **Performance**: Background validation runs efficiently every 5 seconds without impacting battery life

## Backward Compatibility

- All existing functionality preserved
- Enhanced validation runs alongside existing tracking
- No breaking changes to public APIs
- Maintains integration with CoreBatteryStatsService

## Monitoring and Debugging

Use these log tags to monitor the fix:
- `GAP_VALIDATION`: Background validation process
- `SCREEN_TIME_GAP_VIOLATION`: When gaps exceed 60 seconds
- `SIMPLIFIED_OFF_GAP`: Gap detection during Screen OFF calculations
- `FORCE_SET_OFF_TIME`: When corrections are applied
