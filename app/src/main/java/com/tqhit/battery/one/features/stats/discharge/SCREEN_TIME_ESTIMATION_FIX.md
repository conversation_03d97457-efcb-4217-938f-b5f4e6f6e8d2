# Screen Time Estimation Fix - App Restart Issue

## Problem Description

After refactoring from the `new_discharge` module to the `stats/discharge` module, there was an issue where screen on/off time estimates were not being properly calculated or restored when the application restarted. This resulted in inconsistent or incorrect screen time displays in the UI.

## Root Cause Analysis

The investigation revealed several interconnected issues:

### 1. **Screen State Initialization Race Condition**
- During app restart, the `DischargeSessionRepository.loadCachedSession()` method was initializing the `ScreenStateTimeTracker` without properly verifying the actual current screen state
- The repository assumed the screen state without checking `PowerManager.isInteractive`
- This led to mismatched internal state tracking vs. actual device state

### 2. **Gap Estimation vs. UI Time Tracking Mismatch**
- The `FullSessionReEstimator` correctly calculated screen times for the entire session from start to current time
- However, the `ScreenStateTimeTracker` (used for UI updates) was initialized separately and not properly synchronized with the re-estimated values
- This caused the UI to show different times than what was calculated by the session logic

### 3. **Missing Screen State Verification**
- There was no periodic verification to ensure the internal screen state tracking matched the actual device state
- Screen state changes during app closure were not properly handled during restart

### 4. **Time Consistency Issues**
- The UI time tracker could potentially go backwards in time if re-initialized with older session data
- No validation was in place to ensure time consistency between different tracking components

## Solution Implemented

### 1. **Enhanced Session Loading with Screen State Verification**
**File:** `DischargeSessionRepository.kt` - `loadCachedSession()` method

```kotlin
// Verify and initialize screen state properly
val actualScreenState = powerManager.isInteractive
Log.d(TAG, "SCREEN_INIT: Actual screen state during session restore: ${if (actualScreenState) "ON" else "OFF"}")

// Initialize screen time tracker with session data and verified screen state
screenStateTimeTracker.initialize(
    cachedSession.screenOnTimeMillis,
    cachedSession.screenOffTimeMillis,
    actualScreenState
)

// Update internal tracking to match actual state
lastScreenState = actualScreenState
lastScreenStateChangeTime = System.currentTimeMillis()
```

### 2. **Improved Gap Estimation Synchronization**
**File:** `DischargeSessionRepository.kt` - `calculateAndApplyGapEstimation()` method

```kotlin
// Re-initialize screen time tracker with re-estimated session data and verified screen state
val actualScreenState = powerManager.isInteractive
screenStateTimeTracker.initialize(
    updatedSession.screenOnTimeMillis,
    updatedSession.screenOffTimeMillis,
    actualScreenState
)

// Update internal tracking to match actual state
lastScreenState = actualScreenState
lastScreenStateChangeTime = System.currentTimeMillis()
```

### 3. **Added Screen Time Consistency Verification**
**File:** `DischargeSessionRepository.kt` - New `verifyScreenTimeConsistency()` method

```kotlin
private fun verifyScreenTimeConsistency(uiOnTime: Long, uiOffTime: Long, session: DischargeSessionData) {
    // Allow some tolerance for timing differences (up to 5 seconds)
    val tolerance = 5000L
    
    val onTimeDiff = kotlin.math.abs(uiOnTime - sessionOnTime)
    val offTimeDiff = kotlin.math.abs(uiOffTime - sessionOffTime)
    
    if (onTimeDiff > tolerance || offTimeDiff > tolerance) {
        // Log warning and potentially re-sync if difference is too large
        if (onTimeDiff > 30000L || offTimeDiff > 30000L) {
            screenStateTimeTracker.initialize(sessionOnTime, sessionOffTime, actualScreenState)
        }
    }
}
```

### 4. **Enhanced ScreenStateTimeTracker Initialization**
**File:** `ScreenStateTimeTracker.kt` - `initialize()` method

```kotlin
// Ensure we don't go backwards in time
val newOnTime = kotlin.math.max(sessionScreenOnTimeMs, currentOnTime)
val newOffTime = kotlin.math.max(sessionScreenOffTimeMs, currentOffTime)

if (newOnTime != sessionScreenOnTimeMs || newOffTime != sessionScreenOffTimeMs) {
    Log.w(TAG, "Adjusted initialization times to prevent going backwards")
}
```

### 5. **Added Proactive Screen State Verification**
**File:** `DischargeViewModel.kt` - Enhanced initialization

```kotlin
// Force screen state check on initialization to ensure consistency
viewModelScope.launch {
    // Give the repository time to initialize
    kotlinx.coroutines.delay(1000)
    Log.d(TAG, "Performing initial screen state verification")
    dischargeSessionRepository.forceCheckScreenState()
}
```

## Testing

### Unit Tests Created
1. **`DischargeSessionRepositoryTest.kt`** - Tests session restoration and gap estimation
2. **`FullSessionReEstimatorTest.kt`** - Tests screen time re-estimation logic
3. **`ScreenTimeEstimationIntegrationTest.kt`** - Integration tests for app restart scenarios

### Test Scenarios Covered
- Session restoration with active session
- Session restoration with inactive session
- Gap estimation on app restart with battery drop
- Screen time UI tracking initialization
- Screen time increment without session
- New session creation when no cached session
- Screen state changes during app closure
- Time consistency verification

## Expected Behavior After Fix

1. **Consistent Screen Time Display**: UI screen times will match session data within acceptable tolerance
2. **Proper State Initialization**: Screen state will be correctly determined during app restart
3. **Accurate Gap Estimation**: Time spent during app closure will be properly estimated and added to session data
4. **Self-Healing**: System will detect and correct inconsistencies automatically
5. **No Time Regression**: Screen times will never go backwards, even during re-initialization

## Monitoring and Logging

Enhanced logging has been added to track:
- Screen state initialization during app restart
- Gap estimation calculations
- Screen time consistency verification
- Time synchronization between UI and session data

Look for log tags:
- `SCREEN_INIT`: Screen state initialization
- `PHASE 2`: Gap estimation and re-estimation
- `SCREEN_TIME_INCONSISTENCY`: Detected inconsistencies
- `SCREEN_TIME_RESYNC`: Automatic re-synchronization events

## Future Improvements

1. **Persistent Screen State Tracking**: Consider saving screen state changes to cache for more accurate gap estimation
2. **Machine Learning**: Use historical patterns to improve gap estimation accuracy
3. **Background Sync**: Implement background service to track screen state changes even when app is closed
4. **User Feedback**: Add UI indicators when gap estimation is applied vs. real-time tracking
