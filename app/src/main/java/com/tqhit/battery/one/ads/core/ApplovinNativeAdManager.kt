package com.tqhit.battery.one.ads.core

import android.content.Context
//import com.applovin.mediation.nativeAds.MaxNativeAdLoader
//import com.applovin.mediation.nativeAds.MaxNativeAdListener
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
//import com.applovin.mediation.nativeAds.MaxNativeAdView
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxError

@Singleton
class ApplovinNativeAdManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
//    private val adUnitId = "b1aebeb926a91949"
//    private var nativeAdLoader: MaxNativeAdLoader? = null
//    private var nativeAd: MaxAd? = null
//
//    fun loadNativeAd(onAdLoaded: (MaxAd) -> Unit, onAdLoadFailed: (String) -> Unit) {
//        nativeAdLoader = MaxNativeAdLoader(adUnitId)
//        nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
//            override fun onNativeAdLoaded(view: MaxNativeAdView?, ad: MaxAd) {
//                nativeAd = ad
//                ad?.let { onAdLoaded(it) }
//            }
//            override fun onNativeAdLoadFailed(adUnitId: String, errorCode: MaxError) {
//                onAdLoadFailed("Native ad failed to load: $errorCode")
//            }
//        })
//        nativeAdLoader?.loadAd()
//    }
//
//    fun destroy() {
//        nativeAdLoader = null
//        nativeAd = null
//    }
}