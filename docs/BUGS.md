On the Discharge module of our app, when device change from charging to discharging, the time on Loss Of Charge section on UI will only start to count after several time. And it seem the total time of session is also not running

Discharge module: app/src/main/java/com/tqhit/battery/one/features/new_discharge


**Potential Problem Areas & Diagnostic Steps:**

1.  **Delayed Detection of "Discharging" State:**
    *   **`AndroidBatteryDataSource` & `NewBatteryRepository`:**
        *   **Check:** Is the `NewBatteryStatus` flow (from `NewBatteryRepository.batteryStatusFlow`) emitting the `isCharging = false` status promptly?
        *   **Log:**
            *   In `AndroidBatteryDataSource`, log when `ACTION_BATTERY_CHANGED` is received and the extracted `isCharging` status.
            *   In `NewBatteryRepository`, log the `liveStatus` it receives and emits, especially the `isCharging` flag and timestamp.
            ```kotlin
            // In NewBatteryRepository, inside batteryStatusFlow.collect
            Log.d(TAG, "REPO: Received live status: isCharging=${liveStatus.isCharging}, %=${liveStatus.percentage}, time=${liveStatus.timestampEpochMillis}")
            ```
    *   **Possible Issue:** Buffering, `distinctUntilChanged` might be too aggressive if other parts of `NewBatteryStatus` don't change much right at the unplug event. (Unlikely for `isCharging` but possible).

2.  **Delayed Start of New Discharge Session in `NewDischargeSessionRepository`:**
    *   **Check:** When `processBatteryStatus(status)` is called with `status.isCharging = false`:
        *   Does it quickly recognize that it needs to end any charging-related state and start a *new discharge session*?
        *   The `when` block is key:
            ```kotlin
            when {
                status.isCharging -> handleChargingState(current) // Ends discharge session
                current == null || !current.isActive -> startNewSession(status) // THIS IS WHAT SHOULD HAPPEN
                // ...
            }
            ```
    *   **Log:**
        *   Inside `NewDischargeSessionRepository.processBatteryStatus()`:
            ```kotlin
            Log.d(TAG, "SESSION_REPO: processBatteryStatus - input.isCharging=${status.isCharging}, currentSession.isActive=${current?.isActive}, currentSession.startPercent=${current?.startPercentage}")
            if (status.isCharging) {
                Log.d(TAG, "SESSION_REPO: Handling charging state.")
            } else if (current == null || !current.isActive) {
                Log.d(TAG, "SESSION_REPO: Condition met to START new discharge session.")
            }
            ```
        *   Inside `SessionManager.createNewSession()`:
            ```kotlin
            Log.d(TAG, "SESSION_MGR: createNewSession called at ${System.currentTimeMillis()} for status.percentage=${status.percentage}%")
            // Log the returned NewDischargeSessionData
            ```
    *   **Possible Issue:** If `handleChargingState(current)` is called (correctly, to end the old session if it was a discharge session that got interrupted by charging), does it set `_currentSession.value` to `null` or `isActive = false` *before* the next `processBatteryStatus` call with `isCharging=false`? If not, the condition `current == null || !current.isActive` might not be met immediately.

3.  **Initialization of Time Trackers for the New Session:**
    *   **`NewDischargeSessionRepository.startNewSession()` (via `SessionManager.createNewSession()`):**
        *   The `NewDischargeSessionData` is created with `screenOnTimeMillis = 0L`, `screenOffTimeMillis = 0L`, and `startTimeEpochMillis = System.currentTimeMillis()`. This looks correct.
        *   **Crucially, `lastScreenStateChangeTime` in `NewDischargeSessionRepository` needs to be reset to `System.currentTimeMillis()` when a new session starts.** If it retains the timestamp from the previous (charging) state, the first screen time calculation will be wrong.
            ```kotlin
            // In NewDischargeSessionRepository, after newSession is created and set to _currentSession.value
            lastScreenStateChangeTime = System.currentTimeMillis() // Or newSession.startTimeEpochMillis
            lastScreenState = powerManager.isInteractive // Ensure this is also current
            Log.d(TAG, "SESSION_REPO: New session started. Reset lastScreenStateChangeTime=${lastScreenStateChangeTime}, lastScreenState=${if(lastScreenState) "ON" else "OFF"}")
            ```
    *   **`ScreenStateTimeTracker` (domain level, used by `NewDischargeSessionRepository` for its UI flows `screenOnTimeUI`, `screenOffTimeUI`):**
        *   `NewDischargeSessionRepository.startNewSession()` calls `screenStateTimeTracker.reset()`. This is good.
        *   **Check:** When is `screenStateTimeTracker.initialize(...)` called for the new discharge session? It should be called with the initial screen state and `0L` for both times. The `loadCachedSession` initializes it, but for a *brand new* session (not from cache), `reset()` is called, but not `initialize()`. `handleScreenStateChange` in the tracker has an init block, but it might be better to explicitly initialize.
            ```kotlin
            // In NewDischargeSessionRepository, after newSession is created:
            screenStateTimeTracker.reset()
            screenStateTimeTracker.initialize(0L, 0L, powerManager.isInteractive) // Explicit initialize
            ```

4.  **Screen State Change Handling (`NewDischargeSessionRepository`):**
    *   **`handleScreenStateChangeEvent(event)` and `handleScreenStateChange(isScreenOn, now)`:**
        *   When transitioning from charging to discharging, the first "discharging" `NewBatteryStatus` arrives. The screen state at this exact moment is used.
        *   If `lastScreenStateChangeTime` was not correctly reset (see point 3a), the `timeInPreviousState` calculated here will be based on an old timestamp, leading to a large incorrect addition to screen on/off time for the new session.
        *   **Log:** Inside these methods, log `event.timestamp`, `now`, `lastScreenStateChangeTime`, `timeInPreviousState`, `wasScreenOff` / `!event.isScreenOn`.
    *   **Possible Issue:** If the *very first* `processBatteryStatus` for discharging also triggers a `handleScreenStateChange` (e.g., if `isScreenOn != lastScreenState`), and `lastScreenStateChangeTime` is old, this will add a huge chunk of incorrect time.

5.  **`DischargeTimerService` Activation and Operation:**
    *   **`NewDischargeViewModel` controls service start/stop:**
        ```kotlin
        val currentSession = dischargeSessionRepository.currentSession.value
        if (currentSession != null && currentSession.isActive && !status.isCharging) {
            if (!dischargeTimerServiceHelper.isServiceRunning()) {
                Log.d(TAG, "VIEW_MODEL: Starting DischargeTimerService. session.isActive=${currentSession.isActive}, status.isCharging=${status.isCharging}")
                dischargeTimerServiceHelper.startService()
            }
        }
        ```
    *   **Possible Issue:** If `dischargeSessionRepository.currentSession.value` is `null` or `isActive` is `false` for a few moments *after* `status.isCharging` becomes `false` (due to delays in `NewDischargeSessionRepository.startNewSession()` propagating), the service won't start immediately.
    *   **`DischargeTimerService.startTracking()`:**
        *   It calls `dischargeSessionRepository.incrementScreenTimeForUI()` every second.
        *   `incrementScreenTimeForUI()` in `NewDischargeSessionRepository` relies on `screenStateTimeTracker.incrementCurrentState()`.
        *   `ScreenStateTimeTracker.incrementCurrentState()` uses `lastIncrementTime` and `lastScreenState`. If these aren't correctly set/reset when the new discharge session starts, the increments will be off.
    *   **Log:**
        *   In `NewDischargeViewModel`, log `currentSession?.isActive` and `status.isCharging` right before the service start logic.
        *   In `DischargeTimerService.startTracking()`, log when it starts.
        *   In `NewDischargeSessionRepository.incrementScreenTimeForUI()` and `ScreenStateTimeTracker.incrementCurrentState()`, log the values being used (`actualElapsedMs`, `lastScreenState`).

6.  **UI Update Path (`NewDischargeViewModel` -> `NewDischargeFragment` -> `DischargeUiUpdater`):**
    *   **`NewDischargeViewModel` collects `dischargeSessionRepository.screenOnTimeUI` and `screenOffTimeUI`:**
        ```kotlin
        _uiState.update { currentState ->
            currentState.copy(
                screenOnTimeUI = screenOnTimeUI,
                screenOffTimeUI = screenOffTimeUI
            )
        }
        ```
    *   **`DischargeUiUpdater.updateLossOfCharge()` uses `state.screenOnTimeUI` and `state.screenOffTimeUI`**.
    *   **Possible Issue:** If the `screenOnTimeUI` and `screenOffTimeUI` flows from `NewDischargeSessionRepository` (which come from `domain.ScreenStateTimeTracker`) are not emitting new values quickly after the session starts, the UI won't update. This again points to the initialization/reset of `domain.ScreenStateTimeTracker`.

**Focus Areas for Debugging:**

1.  **`NewDischargeSessionRepository.processBatteryStatus()`:** Ensure that when `isCharging` transitions to `false`, a new session is started *immediately*, and crucial state like `lastScreenStateChangeTime` is reset.
2.  **`domain.ScreenStateTimeTracker`:** Verify its `reset()` and `initialize()` (or its internal init logic in `handleScreenStateChange`) are correctly setting its internal state (`_screenOnTimeUI.value = 0L`, `_screenOffTimeUI.value = 0L`, `lastStateChangeTime`, `lastIncrementTime`) for the new discharge session.
3.  **`NewDischargeViewModel`'s logic for starting `DischargeTimerService`:** Confirm that the conditions (`currentSession != null && currentSession.isActive && !status.isCharging`) are met promptly.

**Example Logging in `NewDischargeSessionRepository.startNewSession()` (via `SessionManager`):**
```kotlin
// In SessionManager.createNewSession()
Log.i(TAG, "SESSION_MGR: Creating new session. StartTime=${newSession.startTimeEpochMillis}, Start%= ${newSession.startPercentage}, ScreenOnTime=${newSession.screenOnTimeMillis}, ScreenOffTime=${newSession.screenOffTimeMillis}")

// In NewDischargeSessionRepository after calling SessionManager.createNewSession()
_currentSession.value = newSession
currentSessionCache.saveCurrentSession(newSession)

lastScreenState = powerManager.isInteractive
lastScreenStateChangeTime = newSession.startTimeEpochMillis // Use session start time for consistency
Log.i(TAG, "SESSION_REPO: New session started. lastScreenState=${if(lastScreenState)"ON" else "OFF"}, lastScreenStateChangeTime=${lastScreenStateChangeTime}")

screenStateTimeTracker.reset()
screenStateTimeTracker.initialize(0L, 0L, lastScreenState) // Explicitly initialize
Log.i(TAG, "SESSION_REPO: ScreenStateTimeTracker (domain) reset and initialized.")
```

**Total Session Time Not Running:**

*   This is usually because `NewDischargeSessionData.lastUpdateTimeEpochMillis` is not being updated, or the UI is not re-rendering with the new duration.
*   `SessionManager.updateSession()` updates `lastUpdateTimeEpochMillis = now`.
*   The `durationMillis` in `NewDischargeSessionData` is a `get()` property: `lastUpdateTimeEpochMillis - startTimeEpochMillis`.
*   If `SessionManager.updateSession()` isn't called frequently (e.g., if `dischargeCalculator.shouldSkipUpdate()` returns true too often), `lastUpdateTimeEpochMillis` won't change, and thus `durationMillis` won't change.
*   Check the conditions in `shouldSkipUpdate()` and ensure `processBatteryStatus` leads to `updateSession` calls.

By adding targeted logging around these key state transitions and initializations, you should be able to pinpoint where the delay or incorrect state is originating. The transition from charging to discharging is a critical point where multiple components need to reset or re-initialize their state for the new discharge session.