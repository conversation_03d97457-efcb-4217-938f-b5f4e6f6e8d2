## Overall Refactoring Principles for Charge Feature:

1.  **Isolation First:** We'll build the new charge feature in a separate set of classes/packages. This allows us to develop and test it independently before integrating it and replacing the old one.
2.  **Clear Layers (Clean Architecture):**
    *   **Presentation Layer:** New Fragment (`NewChargeFragment`), new ViewModel (`NewChargeViewModel`), and a `ChargeUiState` data class.
    *   **Domain Layer (Use Cases):** Encapsulate specific business logic related to charging (e.g., calculating charge estimates, managing session logic).
    *   **Data Layer (Repositories):** Abstract data sources for battery stats and charge session persistence.
3.  **MVVM for Presentation:** The `NewChargeViewModel` will expose a single `StateFlow<ChargeUiState>` to the `NewChargeFragment`. The Fragment will observe this state and render the UI accordingly. User interactions will be forwarded as events/calls to the ViewModel.
4.  **Hilt for Dependency Injection:** All new components will be Hilt-managed.
5.  **Immutability:** `ChargeUiState` and other data-carrying classes will be data classes, promoting immutability where possible.
6.  **Single Responsibility Principle (SRP):** Break down the existing `BatteryRepository` and `BatteryMonitorService` responsibilities into smaller, focused classes.
7.  **Testing:** Each phase should conclude with unit tests for new components and integration tests where appropriate.

---

## Refactoring Plan: Charge Feature

We will create new components prefixed with "New" or in a new package structure (e.g., `com.tqhit.battery.one.features.charge`) to distinguish from the old implementation.

### Phase 0: Foundation & Core Data Structures

**Goal:** Set up the basic structure and define the data models for the new charge feature.

**Tasks:**

1.  **Create New Package Structure:**
    *   `main/java/com/tqhit/battery/one/features/charge/`
        *   `data/` (for data models and repository interfaces)
        *   `datasource/` (for abstracting raw data sources)
        *   `domain/` (for use cases/interactors)
        *   `presentation/` (for ViewModel and Fragment)
        *   `di/` (for Hilt modules specific to this feature)
2.  **Define Core Data Classes (in `features/charge/data/model/`):**
    *   `NewChargeStatus.kt`:
        ```kotlin
        data class NewChargeStatus(
            val percentage: Int,
            val isCharging: Boolean,
            val pluggedSource: Int, // e.g., BatteryManager.BATTERY_PLUGGED_AC
            val currentMicroAmperes: Long,
            val voltageMillivolts: Int,
            val temperatureCelsius: Float,
            val timestampEpochMillis: Long = System.currentTimeMillis()
        )
        ```
    *   `NewChargeSession.kt`: (Similar to existing `ChargeSession` but potentially refined)
        ```kotlin
        data class NewChargeSession(
            val id: String = UUID.randomUUID().toString(), // For easier DB management if we switch later
            val startTimeEpochMillis: Long,
            var endTimeEpochMillis: Long? = null,
            val startPercentage: Int,
            var endPercentage: Int? = null,
            var totalMahCharged: Double = 0.0, // Calculated
            var screenOnTimeMillis: Long = 0L,
            var screenOffTimeMillis: Long = 0L,
            // Add other relevant metrics like avg speed, avg mA for screen on/off during this session
            var avgScreenOnChargeRateMahPerHour: Double = 0.0,
            var avgScreenOffChargeRateMahPerHour: Double = 0.0,
            var avgMixedChargeRateMahPerHour: Double = 0.0,
            var avgPercentPerHour: Double = 0.0
        )
        ```
    *   `ChargeEstimates.kt`:
        ```kotlin
        data class ChargeEstimates(
            val timeToFullMillis: Long,
            val timeToTargetPercentageMillis: Long,
            val targetPercentage: Int,
            val currentChargingRatePercentPerHour: Double, // Instantaneous or short-term average
            val averageSessionChargeRatePercentPerHour: Double // Average for the current session
        )
        ```
    *   `ChargeUiState.kt` (in `features/charge/presentation/`): Initial empty version.
        ```kotlin
        data class ChargeUiState(
            val isLoading: Boolean = true,
            val chargeStatus: NewChargeStatus? = null,
            val activeChargeSession: NewChargeSession? = null,
            val chargeEstimates: ChargeEstimates? = null,
            val lastValidChargeSessionSummary: String? = null, // For display when not charging
            val error: String? = null
            // Add other UI-specific fields as needed
        )
        ```
3.  **Define Core Interfaces (in `features/charge/data/repository/` and `features/charge/domain/`):**
    *   `BatteryStatsDataSource.kt`: Interface to provide raw `Flow<NewChargeStatus>`.
    *   `ChargeSessionRepository.kt`: Interface for CRUD operations on `NewChargeSession` (e.g., `getLatestActiveSession()`, `saveSession()`, `getAllSessions()`).
    *   `ChargeEstimatorUseCase.kt`: Interface for calculating charge estimates.
    *   `ManageChargeSessionUseCase.kt`: Interface for starting, updating, and ending charge sessions.

**Testing:**
*   N/A for this phase (mainly definitions).

---

### Phase 1: Data Sourcing & Core Domain Logic

**Goal:** Implement the data sources and core calculation logic, independent of the UI.

**Tasks:**

1.  **Implement `AndroidBatteryStatsDataSource.kt` (in `features/charge/datasource/`):**
    *   Similar to `new_discharge/datasource/AndroidBatteryDataSource.kt`.
    *   Uses `BroadcastReceiver` for `ACTION_BATTERY_CHANGED`.
    *   Transforms `Intent` data into `NewChargeStatus`.
    *   Provides `Flow<NewChargeStatus>`.
    *   Inject with Hilt.
2.  **Implement `ChargeDataRepository.kt` (in `features/charge/data/repository/`):**
    *   Depends on `BatteryStatsDataSource`.
    *   Exposes `Flow<NewChargeStatus>`.
    *   Could potentially add caching here if `BatteryStatsDataSource` is very raw.
    *   Inject with Hilt.
3.  **Implement `PrefsChargeSessionRepository.kt` (in `features/charge/data/repository/`):**
    *   Implements `ChargeSessionRepository`.
    *   Uses `PreferencesHelper` (or Room if preferred for more complex querying later, but Prefs is fine for now to keep it simple and isolated).
    *   Manages storing and retrieving `NewChargeSession` data.
        *   Use new preference keys to avoid conflicts (e.g., `charge_current_session`, `charge_session_history`).
    *   Inject with Hilt.
4.  **Implement `CalculateChargeEstimatesUseCase.kt` (in `features/charge/domain/`):**
    *   Implements `ChargeEstimatorUseCase`.
    *   Takes `NewChargeStatus`, current `NewChargeSession` (if any), target percentage, and historical average charging rates (can be simple defaults initially).
    *   Calculates `ChargeEstimates` (time to full, time to target). Logic can be adapted from `BatteryRepository.calculateChargingTimeRemaining`.
    *   This should be pure Kotlin, testable.
    *   Inject with Hilt.
5.  **Implement `DefaultManageChargeSessionUseCase.kt` (in `features/charge/domain/`):**
    *   Implements `ManageChargeSessionUseCase`.
    *   Depends on `ChargeSessionRepository` and `ChargeDataRepository`.
    *   Listens to `Flow<NewChargeStatus>`.
    *   Logic to:
        *   Start a new `NewChargeSession` when charging begins.
        *   Update the active `NewChargeSession` with new percentages, calculate average rates for the session, update `totalMahCharged` (based on percentage change and battery capacity).
        *   End the `NewChargeSession` when charging stops or is complete.
    *   This use case will be central to managing the lifecycle of a charge.
    *   Inject with Hilt.
6.  **Hilt Module (in `features/charge/di/`):**
    *   `ChargeFeatureModule.kt` to provide implementations for the new interfaces.

**Testing:**
*   Unit tests for `CalculateChargeEstimatesUseCase` with various inputs.
*   Unit tests for `DefaultManageChargeSessionUseCase` (mocking repositories) to verify session start, update, and end logic.
*   Unit tests for `PrefsChargeSessionRepository` to ensure saving/loading sessions works.
*   (Optional) Instrumented test for `AndroidBatteryStatsDataSource` if feasible to simulate battery broadcasts.

---

### Phase 2: ViewModel and UI State Preparation

**Goal:** Create the ViewModel that will consume the new domain logic and prepare a unified UI state.

**Tasks:**

1.  **Implement `NewChargeViewModel.kt` (in `features/charge/presentation/`):**
    *   Inject `ChargeDataRepository`, `ManageChargeSessionUseCase`, `CalculateChargeEstimatesUseCase`, `AppRepository` (for target percentage from existing settings).
    *   `private val _uiState = MutableStateFlow(ChargeUiState())`
    *   `val uiState: StateFlow<ChargeUiState> = _uiState.asStateFlow()`
    *   Collect `Flow<NewChargeStatus>` from `ChargeDataRepository`.
    *   Collect `Flow<NewChargeSession?>` from `ManageChargeSessionUseCase` (or `ChargeSessionRepository` if the use case exposes it).
    *   When data changes, invoke `CalculateChargeEstimatesUseCase` to get `ChargeEstimates`.
    *   Combine all information into a new `ChargeUiState` object and update `_uiState.value`.
    *   Expose functions for user interactions, e.g., `fun setTargetChargePercentage(percentage: Int)`. This would persist via `AppRepository` and trigger recalculation of estimates.
    *   Handle loading states and potential errors.
2.  **Refine `ChargeUiState.kt`:**
    *   Add all necessary fields that the new UI will need, based on the data available from `NewChargeStatus`, `NewChargeSession`, and `ChargeEstimates`.
    *   Example fields:
        *   `batteryPercentage: Int`
        *   `isCharging: Boolean`
        *   `voltageText: String` (formatted)
        *   `powerText: String` (formatted)
        *   `amperageText: String` (formatted)
        *   `temperatureText: String` (formatted)
        *   `timeToFullText: String` (formatted)
        *   `timeToTargetText: String` (formatted)
        *   `sessionDurationText: String` (formatted)
        *   `sessionChargedPercentText: String` (formatted)
        *   etc.

**Testing:**
*   Unit tests for `NewChargeViewModel` (mocking use cases/repositories):
    *   Verify correct `ChargeUiState` emission upon new data from flows.
    *   Test state updates when user interactions are simulated (e.g., changing target percentage).
    *   Test loading and error states.

---

### Phase 3: New UI Implementation

**Goal:** Create a new `NewChargeFragment` with a modular layout structure, using the `NewChargeViewModel` and `ChargeUiState`.

**Tasks:**

1.  **Create Modular Layout Files (in `res/layout/sections_charge/`):**
    Create a subfolder named sections_charge inside res/layout/.
    For each logical section identified above, create a separate XML layout file. These will be included in the main fragment layout.
    *   `section_charge_main_display.xml`:
        *   Will contain the `percent_layout` (circular progress) and `time_num` (time estimates) from the old `fragment_charge.xml`.
        *   Views will need new IDs if they are to be accessed directly, or the section can be managed by a dedicated binding class if complex.
    *   `section_charge_battery_wear.xml`:
        *   Contains the UI elements for battery wear calculation (title, seekbar, descriptive texts).
    *   `section_charge_status_details.xml`:
        *   Contains the "Charging status" section with rows for voltage, power, amperage, temperature, and average charging speed (current, not session average).
    *   `section_charge_remaining_time.xml`:
        *   Contains the "Remaining charging time" section with estimates for time to target and time to 100%.
    *   `section_charge_current_session.xml`:
        *   Contains the "Current session" details block with all its sub-sections (total time, current rate, average speed, total charged, screen on/off stats for the session).
    *   `section_charge_overall_average.xml`:
        *   Contains the "Average speed charge" (overall averages across sessions) section.
    *   `section_charge_history_graph.xml`:
        *   Contains the amperage/power graph and its associated controls (timeframe buttons, etc.).

2.  **Create `fragment_charge.xml` (Main Container Layout):**
    *   This will be a `NestedScrollView` containing a `LinearLayout` (or `ConstraintLayout`) that primarily uses `<include>` tags to bring in the section layouts.
    *   Example structure:
        ```xml
        <androidx.core.widget.NestedScrollView
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include
                    android:id="@+id/chargeMainDisplaySection"
                    layout="@layout/section_charge_main_display" />

                <TextView
                    android:id="@+id/newChargeNotChargingMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/not_charging_message"
                    android:visibility="gone"
                    style="@style/YourNotChargingMessageStyle" />

                <include
                    android:id="@+id/chargeBatteryWearSection"
                    layout="@layout/section_charge_battery_wear" />

                <include
                    android:id="@+id/chargeStatusDetailsSection"
                    layout="@layout/section_charge_status_details" />

                <include
                    android:id="@+id/chargeRemainingTimeSection"
                    layout="@layout/section_charge_remaining_time" />

                <include
                    android:id="@+id/chargeCurrentSessionSection"
                    layout="@layout/section_charge_current_session" />

                <include
                    android:id="@+id/chargeOverallAverageSection"
                    layout="@layout/section_charge_overall_average" />

                <include
                    android:id="@+id/chargeHistoryGraphSection"
                    layout="@layout/section_charge_history_graph" />

                <!-- Bottom margin to avoid overlap with navigation bar -->
                <Space
                    android:layout_width="match_parent"
                    android:layout_height="90dp"/>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
        ```
    *   Ensure appropriate margins and styling for each included section within their respective files.
    *   Use `ViewBinding`. The main `FragmentNewChargeBinding` will give you access to the included layouts via their IDs (e.g., `binding.chargeMainDisplaySection.someTextViewId`).

3.  **Implement `NewChargeFragment.kt` (in `features/charge/presentation/`):**
    *   Extend `Fragment` (or your base fragment).
    *   Inject `NewChargeViewModel` using `by viewModels()`.
    *   In `onViewCreated`, collect `viewModel.uiState`.
    *   **Data Binding:** Update UI elements within *each included section* based on the `ChargeUiState`.
        *   For example:
            ```kotlin
            // In NewChargeFragment.kt, after collecting uiState
            binding.chargeMainDisplaySection.textPercent.text = "${state.batteryPercentage}%"
            binding.chargeStatusDetailsSection.valVoltage.text = state.voltageText
            // ... and so on for all fields in all sections
            ```
        *   Handle visibility of sections (e.g., hide charging-specific sections if `!state.isCharging`, show `newChargeNotChargingMessage`).
    *   Handle user interactions (e.g., SeekBar for target percentage in `section_charge_battery_wear.xml`) by calling methods on the `NewChargeViewModel`.

4.  **(Optional) Create a Test Activity:**
    *   Like `TestNewDischargeActivity.kt`, create a `TestNewChargeActivity.kt` to host `NewChargeFragment` for isolated testing and UI development. This will be very helpful to verify each section populates correctly.

**Testing (for Phase 3):**
*   UI tests (Espresso) for `NewChargeFragment` hosted in `TestNewChargeActivity`:
    *   Verify UI elements within *each included section* display correct data based on mocked `ChargeUiState`.
    *   Test user interactions within sections (e.g., changing SeekBar updates ViewModel).
    *   Verify visibility changes of sections based on `ChargeUiState`.
---

### Phase 4: Background Service & Notifications Refactoring (Charge-Specific)

**Goal:** Ensure background monitoring and charge-related notifications work with the new architecture. The existing `BatteryMonitorService` is quite broad. We might create a more focused service or refactor it heavily.

**Tasks:**

1.  **Analyze `BatteryMonitorService.kt` for charge-specific logic:**
    *   `checkChargeAlarmConditions()`: This logic needs to be driven by the new data flow.
    *   `checkChargingStateChange()`: Also driven by new data.
    *   Notification updates: These should be based on `ChargeUiState` or specific events from the ViewModel.
2.  **Option A: New `NewChargeMonitorService.kt`**
    *   Focused solely on charge-related background tasks.
    *   Observes flows from `ChargeDataRepository` and/or `ManageChargeSessionUseCase`.
    *   Triggers charge-specific notifications (charge target reached, full charge) based on state.
    *   Handles anti-theft logic related to charging state changes.
    *   This service would be much leaner.
3.  **Option B: Refactor `BatteryMonitorService.kt` (more disruptive to existing)**
    *   Remove old charge logic.
    *   Integrate with new charge feature repositories/use cases for charge-related monitoring.
4.  **`ChargeNotificationManager.kt` (in `features/charge/common/` or similar):**
    *   A Hilt-injected helper class responsible for creating and showing charge-specific notifications.
    *   Takes context and relevant data (e.g., percentage, type of alarm).
    *   The `NewChargeViewModel` (or a dedicated use case called by it/service) would use this manager.
5.  **Alarm Logic:**
    *   The logic for "charge target reached" or "fully charged" should reside in a domain use case (e.g., `CheckChargeAlarmUseCase`).
    *   This use case would be observed by `NewChargeViewModel` (if app is in foreground) or by `NewChargeMonitorService` (if app is in background).
    *   Upon an alarm condition, they would use `ChargeNotificationManager` to show the notification and `VibrationService` to vibrate.

**Testing:**
*   Unit tests for `CheckChargeAlarmUseCase`.
*   Unit tests for `ChargeNotificationManager`.
*   Instrumented tests for `NewChargeMonitorService` (if created) to verify it starts and reacts to simulated battery state changes (this can be complex).

---

### Phase 5: Integration, Deprecation, and Cleanup

**Goal:** Replace the old charge feature with the new one and clean up old code.

**Tasks:**

1.  **Integrate `NewChargeFragment`:**
    *   Modify `nav_graph.xml` to point the "Charge" destination to `com.tqhit.battery.one.features.charge.presentation.NewChargeFragment`.
    *   Update `MainActivity.kt` if it has specific logic tied to the old `ChargeFragment` ID or instance.
2.  **Service Integration:**
    *   Ensure the new or refactored monitoring service is correctly started and managed by the application.
    *   If a new service was created, stop/disable the parts of `BatteryMonitorService` that handled charging.
3.  **Data Migration (If Necessary):**
    *   If `NewChargeSession` has a different structure than the old `ChargeSession` and you want to preserve history, plan a one-time migration from old `PreferencesHelper` keys to new ones. This might be complex and could be deferred if preserving old *charge* session history isn't critical. The prompt implied minimizing touching old files, so new keys are preferred.
4.  **Deprecate Old Components:**
    *   Mark charge-related methods and flows in `BatteryRepository.kt` as `@Deprecated`.
    *   Mark charge-related parts of `BatteryViewModel.kt` as `@Deprecated`.
    *   The old `ChargeFragment.kt` is now unused.
5.  **Gradual Removal:**
    *   After a period of stability, remove the deprecated code and the old `ChargeFragment.xml` and `ChargeFragment.kt`.
    *   Clean up any unused Hilt modules or providers related to the old charge feature.
6.  **Final Testing:**
    *   Full regression testing of the charge feature and related functionalities (alarms, notifications).
    *   Test interactions with other features if any.

The bundle id is "com.fc.p.tj.charginganimation.batterycharging.chargeeffect", use it to run debug our app when you need