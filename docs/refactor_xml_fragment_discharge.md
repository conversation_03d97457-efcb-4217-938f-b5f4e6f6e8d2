**Step 1: Analyze `fragment_discharge.xml` and Identify Sections**

Based on the original `fragment_discharge.xml` and the string names, here are the logical sections I can identify. We'll map them to their "real names" from `strings.xml` for the new file names.

*   **A. Basic Percentage & Time Estimates Block:**
    *   Original Views: `discharge_text_percent`, `discharge_text_percent3`, `day_block`, `all_block`, `night_block` and their children (`discharge_sun`, `discharge_all`, `discharge_night`).
    *   Real Name/Concept: "Current Battery Status & Basic Time Estimates"
    *   New File: `layout_discharge_section_status_and_estimates.xml`

*   **B. "Info in Current Session" (Loss of Charge) Block:**
    *   Original Views: The `ConstraintLayout` containing "Info in current session" title, `discharge_rate_info` (info button), and the two sub-blocks for "Screen On" (`i_1` and its children like `time_day_session`, `info_day_percent_session`, `info_day_speed_session`) and "Screen Off" (`i_2` and its children).
    *   Real Name/Concept: "Loss of Charge in Current Session"
    *   New File: `layout_discharge_section_loss_of_charge.xml`

*   **C. "Current Session" Details Block:**
    *   Original Views: The `ConstraintLayout` with title `st_text` ("Current session"), `discharge_session_info` (info button), and its 6 sub-blocks (`s_1` to `s_6`) displaying various session metrics.
    *   Real Name/Concept: "Current Discharge Session Details"
    *   New File: `layout_discharge_section_current_session_details.xml`

*   **D. "Operating Time with Screen Off" Block:**
    *   Original Views: The `ConstraintLayout` with title `st_texeet` ("Operating time"), `operation_session_info` (info button), and its sub-blocks for "Awake" (`s_7`) and "Deep sleep" (`s_8`).
    *   Real Name/Concept: "Operating Time (Screen Off)"
    *   New File: `layout_discharge_section_operating_time_screen_off.xml`

*   **E. "Average Battery Usage" Block:**
    *   Original Views: The `ConstraintLayout` with title `i_text` ("Using baterry_middle"), `average_info` (info button), and its sub-blocks for "Screen On" (`i1`), "Screen Off" (`i2`), and "Complex Use" (`i3`).
    *   Real Name/Concept: "Average Battery Usage"
    *   New File: `layout_discharge_section_average_usage.xml`

*   **F. "Full Battery Time Estimates" Block:**
    *   Original Views: The `ConstraintLayout` with `id="@+id/indent_down"`, title `f_text` ("Timework_on_fullbaterry"), `full_percent_info` (info button), and its sub-blocks for "Screen On" (`f1`), "Screen Off" (`f2`), and "Complex Use" (`f3`).
    *   Real Name/Concept: "Full Battery Time Estimates"
    *   New File: `layout_discharge_section_full_battery_estimates.xml`

*   **G. Alarm & Reset Buttons Block:**
    *   Original Views: `battery_alarm_btn`, `reset_session_discharge_button`. These are currently standalone buttons within larger `ConstraintLayout` parents in `fragment_discharge.xml`. We can group them or keep them in the main `new_fragment_discharge.xml` if they don't fit neatly into a content section. For now, let's plan to include them in a general "Actions" block or keep them in the parent.
    *   Real Name/Concept: "Actions"
    *   New File (Optional): `layout_discharge_section_actions.xml` (or integrate into parent)

**Step 2: Create New Layout Files with Renamed IDs**

We will now create the new XML files. For each, I'll show the structure and some example ID renames.

**A. `layout_discharge_section_status_and_estimates.xml`**
(Corresponds to the top block with main percentage and the three time estimations: Screen On, All, Night)

```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/statusAndEstimatesRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp">

    <!-- Main Percentage Display -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/sae_tv_percentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="27dp"
            android:textColor="?attr/colorr"
            android:text="@string/zero"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="8dp"/>
            <!-- Corresponds to original discharge_text_percent -->

        <TextView
            android:id="@+id/sae_tv_formatted_status_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="?attr/black"
            android:textSize="14sp"
            android:gravity="start|center_vertical"
            android:ellipsize="marquee"
            android:singleLine="true"
            app:layout_constraintStart_toEndOf="@id/sae_tv_percentage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/sae_tv_percentage"
            app:layout_constraintBottom_toBottomOf="@id/sae_tv_percentage"
            android:text="@string/charging"/>
            <!-- Corresponds to original discharge_text_percent3 -->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Time Estimates Table (originally three separate RelativeLayouts: day_block, all_block, night_block) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="5dp">

        <!-- Screen On Estimate -->
        <RelativeLayout
            android:id="@+id/sae_rl_screen_on_estimate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/grey_block_line_up_static"
            android:paddingVertical="12dp"
            android:paddingHorizontal="10dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/active_mode"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"/>
            <TextView
                android:id="@+id/sae_tv_screen_on_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/zero"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"/>
                <!-- Corresponds to original discharge_sun -->
        </RelativeLayout>

        <!-- Mixed Usage Estimate -->
        <RelativeLayout
            android:id="@+id/sae_rl_mixed_usage_estimate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/grey_block_line"
            android:layout_marginTop="5dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="10dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/complex_use"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"/>
            <TextView
                android:id="@+id/sae_tv_mixed_usage_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/zero"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"/>
                <!-- Corresponds to original discharge_all -->
        </RelativeLayout>

        <!-- Screen Off Estimate -->
        <RelativeLayout
            android:id="@+id/sae_rl_screen_off_estimate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/grey_block_line_down"
            android:layout_marginTop="5dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="10dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/screen_off"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"/>
            <TextView
                android:id="@+id/sae_tv_screen_off_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/zero"
                android:textColor="?attr/black"
                android:textSize="14sp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"/>
                <!-- Corresponds to original discharge_night -->
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>
```

**B. `layout_discharge_section_loss_of_charge.xml`**
(Corresponds to "Info in current session" which is "Loss of charge")

```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/lossOfChargeRoot"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:paddingTop="7dp"
    android:paddingBottom="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginTop="14dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/loc_tv_title"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/info_in_current_session"
            android:layout_marginStart="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/loc_iv_info_button"
            app:layout_constraintTop_toTopOf="parent"/>
            <!-- Original i_t -->
        <ImageView
            android:id="@+id/loc_iv_info_button"
            android:layout_width="22sp"
            android:layout_height="0dp"
            android:src="@drawable/ic_note"
            android:scaleType="fitEnd"
            android:layout_marginStart="5dp"
            app:layout_constraintTop_toTopOf="@id/loc_tv_title"
            app:layout_constraintBottom_toBottomOf="@id/loc_tv_title"
            app:layout_constraintEnd_toEndOf="parent"/>
            <!-- Original discharge_rate_info -->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Screen On Consumption in Current Session -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loc_cl_screen_on_consumption"
        android:background="@drawable/grey_block_static"
        android:paddingVertical="7dp"
        android:paddingHorizontal="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">
        <TextView
            android:id="@+id/loc_tv_screen_on_title"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/active_mode"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/loc_tv_screen_on_percentage_dropped"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_on_title"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_day_percent_session -->
        <TextView
            android:id="@+id/loc_tv_screen_on_percentage_unit"
            android:textSize="11sp"
            android:textColor="?attr/black"
            android:text="@string/percent"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_on_percentage_dropped"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_on_percentage_dropped"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/loc_tv_screen_on_mah_consumed"
            android:textSize="14sp"
            android:textColor="?attr/colorr"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_on_percentage_dropped"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_day_speed_session -->
        <TextView
            android:id="@+id/loc_tv_screen_on_mah_unit"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/ma_in_medium"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_on_mah_consumed"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_on_mah_consumed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
         <!-- Original time_day_session was for "Operating Time", not directly for "Loss of Charge".
              We will add a separate TextView if session-specific screen-on DURATION is needed here.
              The old layout used `time_day_session` from a different block. -->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Screen Off Consumption in Current Session -->
     <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loc_cl_screen_off_consumption"
        android:background="@drawable/grey_block_static"
        android:paddingVertical="7dp"
        android:paddingHorizontal="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"> <!-- Margin between screen on/off blocks -->
        <TextView
            android:id="@+id/loc_tv_screen_off_title"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/screen_off"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/loc_tv_screen_off_percentage_dropped"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_off_title"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_night_percent_session -->
        <TextView
            android:id="@+id/loc_tv_screen_off_percentage_unit"
            android:textSize="11sp"
            android:textColor="?attr/black"
            android:text="@string/percent"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_off_percentage_dropped"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_off_percentage_dropped"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/loc_tv_screen_off_mah_consumed"
            android:textSize="14sp"
            android:textColor="?attr/colorr"
            android:text="@string/zero"
            app:layout_constraintTop_toBottomOf="@id/loc_tv_screen_off_percentage_dropped"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
            <!-- Original info_night_speed_session -->
        <TextView
            android:id="@+id/loc_tv_screen_off_mah_unit"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:text="@string/ma_in_medium"
            app:layout_constraintBaseline_toBaselineOf="@id/loc_tv_screen_off_mah_consumed"
            app:layout_constraintStart_toEndOf="@id/loc_tv_screen_off_mah_consumed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <!-- Button "App power consumption" (button_discharge_using) - decide if it belongs here or in a general actions block -->
</LinearLayout>
```

**C. `layout_discharge_section_current_session_details.xml`**
(Corresponds to the block with title "Current session")

```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/currentSessionDetailsRoot"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:paddingVertical="7dp"
    android:paddingHorizontal="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="14dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/csd_tv_title"
            android:textSize="19sp"
            android:textColor="?attr/black"
            android:text="@string/current_session"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="2dp"
            android:layout_width="0dp"
            app:layout_constraintEnd_toStartOf="@+id/csd_iv_info_button"
            android:layout_height="wrap_content"/>
            <!-- Original st_text -->
        <ImageView
            android:id="@+id/csd_iv_info_button"
            android:layout_width="22sp"
            android:layout_height="0dp"
            android:src="@drawable/ic_note"
            android:scaleType="fitEnd"
            android:layout_marginStart="5dp"
            app:layout_constraintTop_toTopOf="@id/csd_tv_title"
            app:layout_constraintBottom_toBottomOf="@id/csd_tv_title"
            app:layout_constraintEnd_toEndOf="parent"/>
            <!-- Original discharge_session_info -->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 1: Total Time, Current Rate -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp">
        <!-- Total Time Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_total_time"
            android:background="@drawable/grey_block_static"
            android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_current_rate"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_total_time_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/all_time_charge"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_total_time_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero_seconds"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_time_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_fulltime_dis_session -->
            <TextView
                android:id="@+id/csd_tv_session_start_time"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_time_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original time_dis_session_start -->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Current Rate Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_current_rate"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_total_time"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_total_time"
            app:layout_constraintStart_toEndOf="@id/csd_cl_total_time"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
             <TextView
                android:id="@+id/csd_tv_current_rate_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/now"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_current_rate_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_current_rate_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_now_dis_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_current_rate_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_current_rate_value"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 2: Average Speed, Total Consumed -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">
        <!-- Average Speed Block -->
         <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_avg_speed"
            android:background="@drawable/grey_block_static"
            android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_total_consumed"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_avg_speed_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/average_speed"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_avg_speed_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_avg_speed_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_all_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_avg_speed_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_avg_speed_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_avg_speed_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_avg_speed_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_all_session -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_avg_speed_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_avg_speed_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Total Consumed Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_total_consumed"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_avg_speed"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_avg_speed"
            app:layout_constraintStart_toEndOf="@id/csd_cl_avg_speed"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
            <TextView
                android:id="@+id/csd_tv_total_consumed_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/all"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_total_consumed_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_consumed_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_session_last -->
            <TextView
                android:id="@+id/csd_tv_total_consumed_percent_unit_and_range"
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent" 
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_total_consumed_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_total_consumed_percent_value"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>
                <!-- Combined original textView_percent and discharge_session_percent -->
            <TextView
                android:id="@+id/csd_tv_total_consumed_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_total_consumed_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_day_session (misused for total mAh) -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_total_consumed_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_total_consumed_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Row 3: Screen Off Stats, Screen On Stats -->
     <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">
        <!-- Screen Off Session Stats Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_screen_off_stats"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/csd_cl_screen_on_stats"
            android:layout_marginEnd="4dp">
            <TextView
                android:id="@+id/csd_tv_screen_off_stats_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/screen_off"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_off_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_off_stats_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_night_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_off_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_off_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_off_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_off_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_night_session -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_off_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_off_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Screen On Session Stats Block -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/csd_cl_screen_on_stats"
            android:background="@drawable/grey_block_static"
             android:paddingVertical="7dp"
            android:paddingHorizontal="9dp"
            android:layout_width="0dp"
            android:layout_height="0dp" 
            app:layout_constraintTop_toTopOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintBottom_toBottomOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintStart_toEndOf="@id/csd_cl_screen_off_stats"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="4dp">
             <TextView
                android:id="@+id/csd_tv_screen_on_stats_label"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/active_mode"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_on_percent_value"
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_on_stats_label"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_percent_dis_day_session -->
            <TextView
                android:textSize="11sp"
                android:textColor="?attr/black"
                android:text="@string/percent_in_hour"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_on_percent_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_on_percent_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/csd_tv_screen_on_mah_value"
                android:textSize="14sp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintTop_toBottomOf="@id/csd_tv_screen_on_percent_value"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
                <!-- Original text_speed_dis_day_session2 -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:text="@string/ma_in_medium"
                app:layout_constraintBaseline_toBaselineOf="@id/csd_tv_screen_on_mah_value"
                app:layout_constraintStart_toEndOf="@id/csd_tv_screen_on_mah_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
```

**D. `layout_discharge_section_operating_time_screen_off.xml`**
**E. `layout_discharge_section_average_usage.xml`**
**F. `layout_discharge_section_full_battery_estimates.xml`**

*   These will follow a similar pattern:
    *   Root `LinearLayout` or `ConstraintLayout` with an `android:id` (e.g., `operatingTimeRoot`).
    *   Internal structure mimicking the original `fragment_discharge.xml` for that section.
    *   Rename IDs with a prefix (e.g., `ots_tv_awake_duration` for "Operating Time Screen-off - TextView Awake Duration").
    *   Apply `@drawable/white_block` background to the root and `@drawable/grey_block_static` (or similar) to internal sub-blocks.
    *   Ensure titles and info buttons (`ImageView` with `ic_note`) are included and correctly ID'd.

**G. `layout_discharge_section_actions.xml` (Optional, for buttons)**

```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/actionsRoot"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="8dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginTop="14dp">

    <TextView
        android:id="@+id/actions_btn_battery_alarm"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:gravity="center"
        android:background="@drawable/grey_block"
        android:paddingVertical="12.5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/low_battery_alarm" />
        <!-- Original battery_alarm_btn -->

    <TextView
        android:id="@+id/actions_btn_reset_session"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:gravity="center"
        android:background="@drawable/grey_block"
        android:paddingVertical="12.5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reset_sessions" />
        <!-- Original reset_session_discharge_button -->
</LinearLayout>
```

**Step 3: Update `new_fragment_discharge.xml` to Use `<include>`**

The new main layout file will be much cleaner:

```xml
<!-- app/src/main/res/layout/new_fragment_discharge.xml -->
<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/new_discharge_scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            android:id="@+id/include_status_and_estimates"
            layout="@layout/layout_discharge_section_status_and_estimates" />

        <include
            android:id="@+id/include_loss_of_charge"
            layout="@layout/layout_discharge_section_loss_of_charge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
            <!-- marginTop applied in the included layout's root or here -->

        <include
            android:id="@+id/include_current_session_details"
            layout="@layout/layout_discharge_section_current_session_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            android:id="@+id/include_operating_time_screen_off"
            layout="@layout/layout_discharge_section_operating_time_screen_off"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <include
            android:id="@+id/include_average_usage"
            layout="@layout/layout_discharge_section_average_usage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
            
        <include
            android:id="@+id/include_full_battery_estimates"
            layout="@layout/layout_discharge_section_full_battery_estimates"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <!-- Actions Block (Alarm and Reset buttons) -->
        <include
            android:id="@+id/include_actions_section"
            layout="@layout/layout_discharge_section_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="90dp"/> <!-- Original bottom margin -->

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
```

**Updating Kotlin Files:**

*   **`NewDischargeFragment.kt`:**
    *   Change `FragmentDischargeBinding` to `NewFragmentDischargeBinding`.
    *   Access views through nested binding objects:
        ```kotlin
        // Example for Phase 0
        if (state.isLoadingInitial) {
            binding.includeStatusAndEstimates.saeTvPercentage.text = "..."
            binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = "Status: ..."
        } else {
            binding.includeStatusAndEstimates.saeTvPercentage.text = "${state.batteryPercentage}%"
            // ...
        }

        // Example for Phase 1
        binding.includeStatusAndEstimates.saeTvScreenOnTime.text = formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)

        // Example for Phase 2
        binding.includeLossOfCharge.locTvScreenOnPercentageDropped.text = ...
        binding.includeCurrentSessionDetails.csdTvTotalTimeValue.text = ...
        
        // Example for Actions block
        binding.includeActionsSection.actionsBtnBatteryAlarm.setOnClickListener { /* ... */ }
        ```
*   **The ViewModel and Repository files do not need to change due to this layout refactoring.** Their responsibility is to provide data, which the fragment then maps to the (now included) views.