**Refined Logic for `NewDischargeSessionRepository` (Handling Resumed Session with Estimated Gap Breakdown - Option B style):**

**Assumptions for this approach:**

1.  We cache the last known average screen-on discharge rate (let's call it `cachedAvgScreenOnRateMah`).
2.  We cache the last known average screen-off discharge rate (`cachedAvgScreenOffRateMah`). These would be updated by `NewBatteryRepository` and saved by `DischargeRatesCache` as planned in Phase 1.
3.  When the app restarts and loads an active cached session, we have:
    *   `cachedSession.startTimeEpochMillis`
    *   `cachedSession.startPercentage`
    *   `cachedSession.lastUpdateTimeEpochMillis` (when the app last saved this session)
    *   `cachedSession.totalMahConsumed` (up to `lastUpdateTimeEpochMillis`)
    *   `cachedSession.screenOnDurationMillis` (up to `lastUpdateTimeEpochMillis`)
    *   `cachedSession.screenOffDurationMillis` (up to `lastUpdateTimeEpochMillis`)
    *   `cachedSession.screenOnMahConsumed` (up to `lastUpdateTimeEpochMillis`)
    *   `cachedSession.screenOffMahConsumed` (up to `lastUpdateTimeEpochMillis`)
4.  We get the `liveStatus` when the app restarts.

**Calculation Steps for the Gap:**

1.  **`gapDurationMillis`**: `liveStatus.timestampEpochMillis - cachedSession.lastUpdateTimeEpochMillis`
2.  **`gapPercentageDropped`**: `cachedSession.currentPercentage` (last known before shutdown) `- liveStatus.percentage` (current live percentage).
    *   *Note:* `cachedSession.currentPercentage` should be saved. If we only saved `startPercentage`, then use that, but it's less accurate if the percentage changed before shutdown. Ideally, `NewDischargeSessionData` should store `currentPercentage` at `lastUpdateTimeEpochMillis`.
3.  **`gapTotalMahConsumed`**: `(gapPercentageDropped / 100.0) * batteryRepository.getEffectiveCapacityMah()`
    *   This is the most reliable measure of energy consumed during the gap, as it's based on the actual change in battery percentage.

4.  **Fetch Cached Discharge Rates:**
    *   `avgScreenOnRate = ratesCache.getAverageScreenOnRateMah() ?: NewDischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA`
    *   `avgScreenOffRate = ratesCache.getAverageScreenOffRateMah() ?: NewDischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA`
    *   Ensure these rates are positive and non-zero. If `avgScreenOnRate` is very close to `avgScreenOffRate`, the system of equations below might be unstable. Handle this edge case (e.g., attribute all to screen off or mixed).

5.  **Estimate Screen On/Off Durations during the Gap:**
    *   Let `t_on` be screen ON time during the gap (in hours).
    *   Let `t_off` be screen OFF time during the gap (in hours).
    *   We have two equations:
        1.  `t_on + t_off = gapDurationMillis / (3600.0 * 1000.0)` (Total time equation)
        2.  `t_on * avgScreenOnRate + t_off * avgScreenOffRate = gapTotalMahConsumed` (Total consumption equation)

    *   This is a system of two linear equations with two variables (`t_on`, `t_off`). We can solve for them.
        From (1): `t_off = (gapDurationMillis / (3600.0 * 1000.0)) - t_on`
        Substitute into (2):
        `t_on * avgScreenOnRate + ((gapDurationMillis / (3600.0 * 1000.0)) - t_on) * avgScreenOffRate = gapTotalMahConsumed`
        `t_on * avgScreenOnRate + (gapDurationHours * avgScreenOffRate) - (t_on * avgScreenOffRate) = gapTotalMahConsumed`
        `t_on * (avgScreenOnRate - avgScreenOffRate) = gapTotalMahConsumed - (gapDurationHours * avgScreenOffRate)`
        `t_on = (gapTotalMahConsumed - (gapDurationHours * avgScreenOffRate)) / (avgScreenOnRate - avgScreenOffRate)`

        *   Calculate `gapEstimatedScreenOnDurationMillis = t_on * 3600.0 * 1000.0`
        *   Calculate `gapEstimatedScreenOffDurationMillis = t_off * 3600.0 * 1000.0`

    *   **Constraints & Edge Cases:**
        *   If `avgScreenOnRate` is very close to `avgScreenOffRate` (e.g., `abs(avgScreenOnRate - avgScreenOffRate) < 5.0` mA), the denominator is near zero, making the solution unstable. In this case:
            *   If `avgScreenOnRate` (and `avgScreenOffRate`) is high, assume mostly screen ON.
            *   If `avgScreenOnRate` is low, assume mostly screen OFF.
            *   Or, use a predefined ratio (e.g., 30% on, 70% off) for the gap duration.
        *   `t_on` and `t_off` must be non-negative and their sum cannot exceed `gapDurationHours`. If calculations yield invalid values (e.g., negative time), clamp them. For instance, if `t_on` is negative, set `t_on = 0` and `t_off = gapDurationHours`. If `t_on > gapDurationHours`, set `t_on = gapDurationHours` and `t_off = 0`.

6.  **Estimate mAh Consumed During Screen On/Off in the Gap:**
    *   `gapScreenOnMahConsumed = (gapEstimatedScreenOnDurationMillis / (3600.0 * 1000.0)) * avgScreenOnRate`
    *   `gapScreenOffMahConsumed = (gapEstimatedScreenOffDurationMillis / (3600.0 * 1000.0)) * avgScreenOffRate`

7.  **Update the Resumed Session:**
    *   `_currentSession.value` (the one loaded from cache and being updated):
        *   `currentPercentage = liveStatus.percentage`
        *   `lastUpdateTimeEpochMillis = System.currentTimeMillis()`
        *   `totalMahConsumed = cachedSession.totalMahConsumed + gapTotalMahConsumed` (More accurate)
            *   *Alternatively, if you trust the rate-based mAh more than the percentage-based mAh:* `totalMahConsumed = cachedSession.totalMahConsumed + gapScreenOnMahConsumed + gapScreenOffMahConsumed`. The percentage-based one is usually better for total consumption during the gap.
        *   `screenOnDurationMillis = cachedSession.screenOnDurationMillis + gapEstimatedScreenOnDurationMillis`
        *   `screenOffDurationMillis = cachedSession.screenOffDurationMillis + gapEstimatedScreenOffDurationMillis`
        *   `screenOnMahConsumed = cachedSession.screenOnMahConsumed + gapScreenOnMahConsumed`
        *   `screenOffMahConsumed = cachedSession.screenOffMahConsumed + gapScreenOffMahConsumed`
        *   `isActive = true`
    *   Save this updated session to `CurrentSessionCache`.

**Impact on the Plan:**

This primarily affects **Phase 2: Current Discharge Session Tracking**, specifically the `NewDischargeSessionRepository`.

*   **`NewDischargeSessionData.kt`:** Needs to store `currentPercentage` (as it was at `lastUpdateTimeEpochMillis`) in addition to `startPercentage`.
    ```kotlin
    data class NewDischargeSessionData(
        // ...
        val startPercentage: Int,
        var currentPercentageAtLastUpdate: Int, // Percentage when last saved/updated
        var currentPercentage: Int, // Live percentage
        // ...
    )
    ```
*   **`cache/CurrentSessionCache.kt` (`PrefsCurrentSessionCache`):** Must save and load `currentPercentageAtLastUpdate`.
*   **`repository/NewDischargeSessionRepository.kt`:**
    *   The `init` block or the first call to `processBatteryStatus` after restart needs to implement the gap calculation logic described above if a cached active session is found and the device is still discharging.
    *   The `processBatteryStatus` method for ongoing updates (when the app *is* running) remains largely the same, calculating deltas since its *own* last processed update.
*   **`repository/NewBatteryRepository.kt`:** Needs to reliably provide `averageScreenOnDischargeRateMah` and `averageScreenOffDischargeRateMah` (which it already plans to do with caching and learning from `DischargeRatesCache`).

**Pros of this approach:**

*   Provides a more complete picture for the *currently displayed* active session by attempting to account for the app's downtime.
*   Leverages learned average consumption rates for a more nuanced estimation than just assuming a single mixed rate.

**Cons:**

*   More complex logic.
*   The estimation for the gap is still an approximation and depends on the accuracy and stability of the cached `avgScreenOnRate` and `avgScreenOffRate`.
*   Edge cases (e.g., rates being too similar, short gaps, very long gaps) need careful handling.

**Full Plan Update (Conceptual - Highlighting Changes):**

The previous "Full Plan" remains largely the same in its phased structure. The key modifications are within the implementation details of Phase 2, specifically `NewDischargeSessionRepository.kt`:

**Phase 0: Foundation & Basic Battery Info (with Initial Caching)**
    *   (No significant changes from the previous full plan, ensure `NewBatteryStatus` includes `currentMicroAmperes` and `timestampEpochMillis`.)

**Phase 1: Core Discharge Time Estimations (Async Calculation, Cached Rates)**
    *   (No significant changes from the previous full plan. `NewBatteryRepository` learns and caches `avgScreenOnRateMah` and `avgScreenOffRateMah` which are crucial for the refined Phase 2.)

**Phase 2: Current Discharge Session Tracking (Async Updates, Persistence for Current Session, **Enhanced Gap Estimation**)**
    *   **`data/NewDischargeSessionData.kt`:** Add `currentPercentageAtLastUpdate: Int`.
    *   **`cache/CurrentSessionCache.kt`:** Ensure it saves/loads `currentPercentageAtLastUpdate`.
    *   **`repository/NewDischargeSessionRepository.kt`:**
        *   **On Initialization/Resumption:**
            *   Load cached session.
            *   If an active session is found and device is still discharging:
                1.  Calculate `gapDurationMillis`, `gapPercentageDropped`, and `gapTotalMahConsumed` (based on percentage drop).
                2.  Fetch `cachedAvgScreenOnRateMah` and `cachedAvgScreenOffRateMah` from `DischargeRatesCache` (via `NewBatteryRepository` or direct injection if `NewDischargeSessionRepository` also manages/learns these, though keeping rate learning in `NewBatteryRepository` is cleaner).
                3.  Solve the system of equations to estimate `gapEstimatedScreenOnDurationMillis` and `gapEstimatedScreenOffDurationMillis`, handling edge cases.
                4.  Calculate `gapScreenOnMahConsumed` and `gapScreenOffMahConsumed` using these estimated durations and cached rates.
                5.  Update the loaded `_currentSession.value` by adding these gap values to the cached session's totals, set live `currentPercentage`, and update `lastUpdateTimeEpochMillis`.
                6.  Save the updated session to `CurrentSessionCache`.
        *   **Ongoing `processBatteryStatus()`:** Calculates deltas based on its *own* last update time while the app is running.
        *   **`endCurrentSessionAndCache()`:** When charging starts, finalize the session. The historical entry (Phase 3) will benefit from the bridged gap data.
    *   `ui/discharge/NewDischargeViewModel.kt`: No major changes, continues to observe `currentSession` from the repository.
    *   `ui/discharge/NewDischargeFragment.kt`: No major changes, continues to display data from the `currentSession` in the UI state. The data itself will now be more "complete" if a gap was bridged.

**Phase 3: Historical Aggregate Statistics & Full Reset Button (Async Aggregation, Caching)**
    *   (No significant changes from the previous full plan. The `historicalSessionManager` will receive `DischargeSession` objects that, if mapped from a bridged `NewDischargeSessionData`, will have more representative total consumption and duration.)

**Phase 4: UI Polish, Dialogs, and Info Buttons (Async Dialog Prep)**
    *   (No significant changes from the previous full plan.)