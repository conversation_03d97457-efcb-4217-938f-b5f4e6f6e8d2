here is the complete, self-contained plan for refactoring the `DischargeFragment`, incorporating performance optimizations and using the existing layout.

**Core Principles for This Refactor:**

1.  **Isolation (Logic):** All new Kotlin logic components (ViewModels, Repositories, DataSources, Calculators, Caches) will be in new files within a dedicated package structure (e.g., `com.tqhit.battery.one.new_discharge.*`).
2.  **Reuse Existing Layout:** The new `NewDischargeFragment.kt` will inflate and bind to the existing `com.tqhit.battery.one.R.layout.fragment_discharge` using ViewBinding.
3.  **MVVM & Hilt:** Adherence to Model-View-ViewModel architecture. `NewDischargeViewModel` will manage UI state and logic, exposing data via `StateFlow`. Repositories will serve as the single source of truth. Hilt will manage all dependency injection.
4.  **Kotlin Best Practices:** Utilize coroutines for asynchronous operations (`Dispatchers.IO` for disk/network, `Dispatchers.Default` for CPU-intensive tasks), immutable data classes for UI states, sealed classes for events/UI states where appropriate, and extension functions for utility.
5.  **Reusability & Single Responsibility (Logic Components):**
    *   Existing managers like `DischargeSessionManager.kt` and `BatteryCalculatorDischarge.kt` will be carefully evaluated. Preference will be given to creating new, cleaner, injectable, and testable components (`NewDischargeSessionRepository.kt`, `NewDischargeCalculator.kt`). If parts of the old managers are robust and can be adapted (e.g., made injectable, methods made suspend functions or Flow-based), they might be reused with caution.
6.  **Testability:** New logic components will be designed for unit testing (Mockito/Turbine).
7.  **Incremental Builds & UI Responsiveness:**
    *   Each phase will result in a buildable and runnable application.
    *   Prioritize displaying *some* data (cached or default/placeholder) immediately to improve perceived performance.
    *   Load/calculate complex or I/O-bound data asynchronously on background threads.
    *   Update the UI reactively as new data becomes available using Kotlin Flows.

---

**Phase 0: Foundation, Basic Battery Info, & Initial Caching**

*   **Goal:**
    *   Set up the new Kotlin classes: `NewDischargeViewModel`, `NewBatteryRepository`, `BatteryDataSource`, and a simple `BatteryStatusCache`.
    *   `NewDischargeFragment` inflates `fragment_discharge.xml`.
    *   Display raw battery percentage (in `binding.dischargeTextPercent`) and charging status (e.g., in `binding.dischargeTextPercent3` or a dedicated status TextView from the layout).
    *   **Performance:** `NewBatteryRepository` will immediately attempt to load the last known basic battery status from `BatteryStatusCache` for quick UI population, then subscribe to live updates from `BatteryDataSource`.
*   **New Kotlin Files to Create:**
    *   `com.tqhit.battery.one.new_discharge.data.NewBatteryStatus.kt`
    *   `com.tqhit.battery.one.new_discharge.datasource.BatteryDataSource.kt` (Interface)
    *   `com.tqhit.battery.one.new_discharge.datasource.AndroidBatteryDataSource.kt` (Implementation)
    *   `com.tqhit.battery.one.new_discharge.cache.BatteryStatusCache.kt` (Interface)
    *   `com.tqhit.battery.one.new_discharge.cache.PrefsBatteryStatusCache.kt` (SharedPreferences Implementation)
    *   `com.tqhit.battery.one.new_discharge.repository.NewBatteryRepository.kt`
    *   `com.tqhit.battery.one.new_discharge.ui.NewDischargeViewModel.kt`
    *   `com.tqhit.battery.one.new_discharge.ui.NewDischargeFragment.kt`

*   **1. `data/NewBatteryStatus.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.data

    import android.os.BatteryManager

    data class NewBatteryStatus(
        val percentage: Int = 0,
        val isCharging: Boolean = false,
        val pluggedSource: Int = 0, // e.g., BatteryManager.BATTERY_PLUGGED_AC
        val currentMicroAmperes: Long = 0L, // For BATTERY_PROPERTY_CURRENT_NOW
        val voltageMillivolts: Int = 0,
        val temperatureCelsius: Float = 0.0f, // Corrected: Stored as actual Celsius
        val timestampEpochMillis: Long = System.currentTimeMillis() // To track freshness
    )
    ```

*   **2. `datasource/BatteryDataSource.kt` (Interface):**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.datasource

    import com.tqhit.battery.one.new_discharge.data.NewBatteryStatus
    import kotlinx.coroutines.flow.Flow

    interface BatteryDataSource {
        fun getBatteryStatusFlow(): Flow<NewBatteryStatus>
        fun getCurrentBatteryStatus(): NewBatteryStatus? // For a one-time synchronous fetch if needed
    }
    ```

*   **3. `datasource/AndroidBatteryDataSource.kt` (Implementation):**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.datasource

    import android.content.BroadcastReceiver
    import android.content.Context
    import android.content.Intent
    import android.content.IntentFilter
    import android.os.BatteryManager
    import com.tqhit.battery.one.new_discharge.data.NewBatteryStatus
    import dagger.hilt.android.qualifiers.ApplicationContext
    import kotlinx.coroutines.channels.awaitClose
    import kotlinx.coroutines.flow.Flow
    import kotlinx.coroutines.flow.buffer
    import kotlinx.coroutines.flow.callbackFlow
    import kotlinx.coroutines.flow.distinctUntilChanged
    import javax.inject.Inject
    import javax.inject.Singleton
    import kotlin.math.roundToInt

    @Singleton
    class AndroidBatteryDataSource @Inject constructor(
        @ApplicationContext private val context: Context
    ) : BatteryDataSource {

        private val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager

        override fun getBatteryStatusFlow(): Flow<NewBatteryStatus> = callbackFlow {
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    intent?.let { trySend(extractBatteryStatus(it)).isSuccess }
                }
            }
            val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
            context.registerReceiver(receiver, filter)

            // Send initial state immediately
            val initialIntent = context.registerReceiver(null, filter)
            initialIntent?.let { trySend(extractBatteryStatus(it)).isSuccess }

            awaitClose {
                try { context.unregisterReceiver(receiver) } catch (e: Exception) { /* Ignored */ }
            }
        }.buffer(kotlinx.coroutines.channels.Channel.CONFLATED) // Emit only the latest
         .distinctUntilChanged() // Only emit if status actually changed based on data class equals()

        override fun getCurrentBatteryStatus(): NewBatteryStatus? {
            val intent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            return intent?.let { extractBatteryStatus(it) }
        }

        private fun extractBatteryStatus(intent: Intent): NewBatteryStatus {
            val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
            val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
            val percentage = if (level != -1 && scale != -1 && scale != 0) (level * 100f / scale).roundToInt() else 0

            val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
            val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                             status == BatteryManager.BATTERY_STATUS_FULL
            val plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

            val currentNowMicroAmperes = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            val voltageMillivolts = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0)
            val temperatureTenthsCelsius = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
            val temperatureCelsius = temperatureTenthsCelsius / 10.0f // Corrected conversion

            return NewBatteryStatus(
                percentage = percentage,
                isCharging = isCharging,
                pluggedSource = plugged,
                currentMicroAmperes = currentNowMicroAmperes,
                voltageMillivolts = voltageMillivolts,
                temperatureCelsius = temperatureCelsius,
                timestampEpochMillis = System.currentTimeMillis()
            )
        }
    }
    ```

*   **4. `cache/BatteryStatusCache.kt` (Interface & Implementation):**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.cache

    import android.content.Context
    import com.google.gson.Gson
    import com.tqhit.battery.one.new_discharge.data.NewBatteryStatus
    import dagger.hilt.android.qualifiers.ApplicationContext
    import kotlinx.coroutines.Dispatchers
    import kotlinx.coroutines.withContext
    import javax.inject.Inject
    import javax.inject.Singleton

    interface BatteryStatusCache {
        suspend fun getLastStatus(): NewBatteryStatus?
        suspend fun saveLastStatus(status: NewBatteryStatus)
    }

    @Singleton
    class PrefsBatteryStatusCache @Inject constructor(
        @ApplicationContext private val context: Context,
        private val gson: Gson // Inject Gson
    ) : BatteryStatusCache {
        private val prefs = context.getSharedPreferences("new_battery_status_cache", Context.MODE_PRIVATE)
        private companion object {
            const val KEY_LAST_STATUS_JSON = "last_status_json"
        }

        override suspend fun getLastStatus(): NewBatteryStatus? = withContext(Dispatchers.IO) {
            val json = prefs.getString(KEY_LAST_STATUS_JSON, null)
            json?.let { gson.fromJson(it, NewBatteryStatus::class.java) }
        }

        override suspend fun saveLastStatus(status: NewBatteryStatus) = withContext(Dispatchers.IO) {
            val json = gson.toJson(status)
            prefs.edit().putString(KEY_LAST_STATUS_JSON, json).apply()
        }
    }
    // Add Gson to Hilt module if not already present:
    // @Provides @Singleton fun provideGson(): Gson = Gson()
    ```

*   **5. `repository/NewBatteryRepository.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.repository

    import android.content.Context
    import android.os.BatteryManager
    import com.tqhit.battery.one.new_discharge.cache.BatteryStatusCache
    import com.tqhit.battery.one.new_discharge.data.NewBatteryStatus
    import com.tqhit.battery.one.new_discharge.datasource.BatteryDataSource
    import com.tqhit.battery.one.repository.AppRepository // Reuse existing for capacity settings
    import dagger.hilt.android.qualifiers.ApplicationContext
    import kotlinx.coroutines.CoroutineScope
    import kotlinx.coroutines.Dispatchers
    import kotlinx.coroutines.SupervisorJob
    import kotlinx.coroutines.flow.*
    import kotlinx.coroutines.launch
    import javax.inject.Inject
    import javax.inject.Singleton

    @Singleton
    class NewBatteryRepository @Inject constructor(
        @ApplicationContext private val context: Context, // For system services
        private val batteryDataSource: BatteryDataSource,
        private val appRepository: AppRepository,
        private val batteryStatusCache: BatteryStatusCache
    ) {
        private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

        val batteryStatusFlow: Flow<NewBatteryStatus> = flow {
            batteryStatusCache.getLastStatus()?.let { emit(it) }
            batteryDataSource.getBatteryStatusFlow().collect { liveStatus ->
                batteryStatusCache.saveLastStatus(liveStatus)
                emit(liveStatus)
            }
        }.distinctUntilChanged()
         .shareIn(repositoryScope, SharingStarted.WhileSubscribed(5000), replay = 1)

        fun getEffectiveCapacityMah(): Int {
            val userSetCapacity = appRepository.getBatteryCapacity()
            if (userSetCapacity > 0) return userSetCapacity

            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            val systemDesignCapacityMicroAh = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_FULL_DESIGN)
            if (systemDesignCapacityMicroAh > 0) return (systemDesignCapacityMicroAh / 1000).toInt()
            
            return 3000 // Default fallback
        }
    }
    ```

*   **6. `ui/discharge/NewDischargeViewModel.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.ui

    import androidx.lifecycle.ViewModel
    import androidx.lifecycle.viewModelScope
    import com.tqhit.battery.one.new_discharge.repository.NewBatteryRepository
    import dagger.hilt.android.lifecycle.HiltViewModel
    import kotlinx.coroutines.flow.MutableStateFlow
    import kotlinx.coroutines.flow.StateFlow
    import kotlinx.coroutines.flow.asStateFlow
    import kotlinx.coroutines.flow.update
    import kotlinx.coroutines.launch
    import javax.inject.Inject

    data class NewDischargeUiState(
        val batteryPercentage: Int = 0,
        val isCharging: Boolean = false,
        val isLoadingInitial: Boolean = true, // For overall initial load
        // Granular loading flags per section will be added
        val screenOnTimeRemainingMs: Long = 0L,
        val screenOffTimeRemainingMs: Long = 0L,
        val mixedUsageTimeRemainingMs: Long = 0L,
        val screenOnTimeAt100PercentMs: Long = 0L,
        val screenOffTimeAt100PercentMs: Long = 0L,
        val mixedUsageTimeAt100PercentMs: Long = 0L,
        val areTimeEstimationsLoading: Boolean = true,
        // Phase 2+
        val currentSession: com.tqhit.battery.one.new_discharge.data.NewDischargeSessionData? = null,
        val isCurrentSessionLoading: Boolean = true,
        // Phase 3+
        val aggregateStats: com.tqhit.battery.one.new_discharge.repository.NewBatteryRepository.AggregateDischargeStats? = null,
        val areAggregatesLoading: Boolean = true
    )

    @HiltViewModel
    class NewDischargeViewModel @Inject constructor(
        private val batteryRepository: NewBatteryRepository
        // Calculator and SessionRepository will be injected in later phases
    ) : ViewModel() {

        private val _uiState = MutableStateFlow(NewDischargeUiState())
        val uiState: StateFlow<NewDischargeUiState> = _uiState.asStateFlow()

        init {
            viewModelScope.launch {
                batteryRepository.batteryStatusFlow.collect { status ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            batteryPercentage = status.percentage,
                            isCharging = status.isCharging,
                            isLoadingInitial = false
                        )
                    }
                }
            }
        }
    }
    ```

*   **7. `ui/discharge/NewDischargeFragment.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.ui

    import android.os.Bundle
    import android.util.Log
    import android.view.LayoutInflater
    import android.view.View
    import android.view.ViewGroup
    import androidx.fragment.app.Fragment
    import androidx.fragment.app.viewModels
    import androidx.lifecycle.Lifecycle
    import androidx.lifecycle.lifecycleScope
    import androidx.lifecycle.repeatOnLifecycle
    import com.tqhit.battery.one.R
    import com.tqhit.battery.one.databinding.FragmentDischargeBinding
    import dagger.hilt.android.AndroidEntryPoint
    import kotlinx.coroutines.launch

    @AndroidEntryPoint
    class NewDischargeFragment : Fragment() {

        private var _binding: FragmentDischargeBinding? = null
        private val binding get() = _binding!!

        private val viewModel: NewDischargeViewModel by viewModels()
        private var previousPercentage: Int = -1 // For animation

        override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
        ): View {
            _binding = FragmentDischargeBinding.inflate(inflater, container, false)
            return binding.root
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            Log.d("NewDischargeFragment", "onViewCreated: Using existing fragment_discharge.xml")
            setInitialSectionVisibility(true) // Hide all complex sections initially

            viewLifecycleOwner.lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.STARTED) {
                    viewModel.uiState.collect { state ->
                        Log.d("NewDischargeFragment", "New UI State: $state")
                        updateBasicInfo(state)
                        // Update other sections based on their loading flags and data in subsequent phases
                    }
                }
            }
        }

        private fun updateBasicInfo(state: NewDischargeUiState) {
            if (state.isLoadingInitial) {
                binding.dischargeTextPercent.text = getString(R.string.state_empty) // Placeholder
                 // Using dischargeTextPercent3 for status text as per original layout
                binding.dischargeTextPercent3.text = getString(R.string.charging, "...") // Placeholder status
            } else {
                // Will be animated in Phase 4
                binding.dischargeTextPercent.text = "${state.batteryPercentage}%"
                // Will be colored in Phase 4
                val statusText = if (state.isCharging) " (Charging)" else " (Discharging)"
                binding.dischargeTextPercent3.text = getString(R.string.charging, state.batteryPercentage.toString()) + statusText
            }
        }

        private fun setInitialSectionVisibility(isLoading: Boolean) {
            val visibility = if (isLoading) View.GONE else View.VISIBLE
            val placeholderVisibility = if (isLoading) View.VISIBLE else View.GONE

            // Example: For time estimation block (Phase 1 target)
            binding.dayBlock.visibility = View.GONE
            binding.allBlock.visibility = View.GONE
            binding.nightBlock.visibility = View.GONE
            binding.indentDown.visibility = View.GONE // Contains "Full battery time estimates"

            // Example: For current session block (Phase 2 target)
            binding.currentSessionBlock.visibility = View.GONE // Main container for current session
            binding.dischargeRateInfo.visibility = View.GONE // Info button within current session
            binding.dischargeSessionInfo.visibility = View.GONE
            binding.operationSessionInfo.visibility = View.GONE


            // Example: For aggregate stats block (Phase 3 target)
            // Assume binding.averageInfo is the parent of these aggregate views in fragment_discharge.xml
            binding.averageInfo.visibility = View.GONE
            
            // Alarm button (Phase 4)
            binding.batteryAlarmBtn.visibility = View.GONE
        }

        override fun onDestroyView() {
            super.onDestroyView()
            _binding = null
        }
    }
    ```

*   **Test Cases (Phase 0):**
    1.  **TC0.1: Cached Data & Live Update on Basic Info:**
        *   Action: Launch app. Close completely. Relaunch. Navigate to `NewDischargeFragment`.
        *   Expected: `binding.dischargeTextPercent` and the status text in `binding.dischargeTextPercent3` show values almost instantly (from cache). These values then update if live data differs. Log cache hits/misses and live data emissions.
    2.  **TC0.2: Charging Status Reactivity:**
        *   Action: Connect/disconnect charger while `NewDischargeFragment` is visible.
        *   Expected: The status text updates correctly.

---

**Phase 1: Core Discharge Time Estimations (Async Calculation, Cached Rates)**

*   **Goal:** Calculate and display "Screen On/Off/Mixed Time Remaining" and "At 100%" equivalents in `binding.dischargeSun`, `binding.dischargeAll`, `binding.dischargeNight`, and `binding.dischargeFulltimeRemainingDay/All/Night`.
    *   **Performance:** Discharge rates (used for estimation) will be cached by `DischargeRatesCache`. Calculations occur in `NewDischargeViewModel`'s background coroutine scope. UI shows "Loading..." or cached estimates initially for these fields.
*   **New Kotlin Files:**
    *   `com.tqhit.battery.one.new_discharge.util.NewDischargeCalculator.kt`
    *   `com.tqhit.battery.one.new_discharge.cache.DischargeRatesCache.kt` (Interface)
    *   `com.tqhit.battery.one.new_discharge.cache.PrefsDischargeRatesCache.kt` (SharedPreferences Impl.)
    *   `com.tqhit.battery.one.new_discharge.util.DateTimeFormatters.kt` (for `formatMillisToHoursMinutes`)
*   **Modified Kotlin Files:**
    *   `repository/NewBatteryRepository.kt` (load/save/learn discharge rates using `DischargeRatesCache`)
    *   `ui/discharge/NewDischargeViewModel.kt` (add logic for time estimations, manage `areTimeEstimationsLoading` flag)
    *   `ui/discharge/NewDischargeFragment.kt` (observe and display, manage visibility of these sections)

*   **1. `util/NewDischargeCalculator.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.util

    import javax.inject.Inject
    import javax.inject.Singleton

    @Singleton
    class NewDischargeCalculator @Inject constructor() {
        companion object {
            const val DEFAULT_AVG_SCREEN_ON_CURRENT_MA = 250.0 // mA
            const val DEFAULT_AVG_SCREEN_OFF_CURRENT_MA = 50.0  // mA
            const val MIXED_USAGE_SCREEN_ON_WEIGHT = 0.4
            const val MIXED_USAGE_SCREEN_OFF_WEIGHT = 0.6
        }

        fun estimateTimeRemainingMillis(currentCapacityMah: Double, averageDischargeRateMah: Double): Long {
            if (averageDischargeRateMah <= 0.001 || currentCapacityMah <= 0) return 0L // Avoid division by zero or tiny rates
            val hours = currentCapacityMah / averageDischargeRateMah
            return (hours * 3600.0 * 1000.0).toLong()
        }
    }
    ```

*   **2. `cache/DischargeRatesCache.kt` (Interface & Prefs Implementation):**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.cache
    // ... (Interface and PrefsDischargeRatesCache as defined in previous detailed plan)
    ```

*   **3. `util/DateTimeFormatters.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.util

    fun formatMillisToHoursMinutes(millis: Long): String {
        if (millis <= 0L) return "0m"
        val totalMinutes = millis / (1000 * 60)
        val hours = totalMinutes / 60
        val minutes = totalMinutes % 60
        return when {
            hours > 0 -> "${hours}h ${String.format("%02d", minutes)}m"
            minutes >= 10 -> "${String.format("%02d", minutes)}m"
            else -> "${minutes}m"
        }
    }
    ```

*   **4. `repository/NewBatteryRepository.kt` (Enhancements):**
    ```kotlin
    // ... (Inject DischargeRatesCache ratesCache) ...
    private val _averageScreenOnDischargeRateMah = MutableStateFlow(NewDischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA)
    val averageScreenOnDischargeRateMah: StateFlow<Double> = _averageScreenOnDischargeRateMah.asStateFlow()

    private val _averageScreenOffDischargeRateMah = MutableStateFlow(NewDischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA)
    val averageScreenOffDischargeRateMah: StateFlow<Double> = _averageScreenOffDischargeRateMah.asStateFlow()

    private val powerManager by lazy { context.getSystemService(Context.POWER_SERVICE) as PowerManager }
    private val screenOnCurrentSamples = mutableListOf<Double>()
    private val screenOffCurrentSamples = mutableListOf<Double>()
    private val MAX_RATE_SAMPLES = 120 // e.g., for 2 minutes of data if sampled per second

    init {
        repositoryScope.launch { // Load cached rates on init
            ratesCache.getAverageScreenOnRateMah()?.let { if (it > 0) _averageScreenOnDischargeRateMah.value = it }
            ratesCache.getAverageScreenOffRateMah()?.let { if (it > 0) _averageScreenOffDischargeRateMah.value = it }
        }

        // Background coroutine to learn and update discharge rates
        repositoryScope.launch(Dispatchers.Default) {
            batteryStatusFlow.collect { status ->
                if (!status.isCharging && status.currentMicroAmperes < -1000) { // Discharging significantly
                    val currentMa = abs(status.currentMicroAmperes / 1000.0)
                    if (powerManager.isInteractive) { // Screen is ON
                        addSampleAndUpdateRate(currentMa, screenOnCurrentSamples, _averageScreenOnDischargeRateMah) {
                            launch { ratesCache.saveAverageScreenOnRateMah(it) }
                        }
                    } else { // Screen is OFF
                        addSampleAndUpdateRate(currentMa, screenOffCurrentSamples, _averageScreenOffDischargeRateMah) {
                            launch { ratesCache.saveAverageScreenOffRateMah(it) }
                        }
                    }
                }
            }
        }
    }

    private fun addSampleAndUpdateRate(
        currentMa: Double,
        samplesList: MutableList<Double>,
        rateFlow: MutableStateFlow<Double>,
        saveAction: suspend (Double) -> Unit
    ) {
        samplesList.add(currentMa)
        if (samplesList.size > MAX_RATE_SAMPLES) samplesList.removeAt(0)
        if (samplesList.isNotEmpty()) {
            val newRate = samplesList.average()
            if (abs(newRate - rateFlow.value) > 0.1) { // Update only if significantly different
                rateFlow.value = newRate
                repositoryScope.launch { saveAction(newRate) }
            }
        }
    }
    ```

*   **5. `ui/discharge/NewDischargeViewModel.kt` (Enhancements):**
    ```kotlin
    // ... (Inject calculator: NewDischargeCalculator)
    init {
        viewModelScope.launch { // Basic info collection
            batteryRepository.batteryStatusFlow
                .collect { status ->
                    _uiState.update { it.copy(
                        batteryPercentage = status.percentage,
                        isCharging = status.isCharging,
                        isLoadingInitial = false)
                    }
                }
        }

        // Separate launch for time estimations, runs on Default dispatcher
        viewModelScope.launch(Dispatchers.Default) {
            combine(
                batteryRepository.batteryStatusFlow, // Already shared, safe to collect again
                batteryRepository.averageScreenOnDischargeRateMah,
                batteryRepository.averageScreenOffDischargeRateMah
            ) { status, screenOnRate, screenOffRate ->
                // Return a data structure or Tuple with all calculated times
                val effectiveCapacityMah = batteryRepository.getEffectiveCapacityMah().toDouble()
                val currentCapacityMah = (status.percentage / 100.0) * effectiveCapacityMah
                // ... (calculations for all 6 time estimations as in previous plan, using calculator)
                // ... ensure rates are positive for calculator
                val positiveScreenOnRate = if (screenOnRate > 0) screenOnRate else NewDischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
                val positiveScreenOffRate = if (screenOffRate > 0) screenOffRate else NewDischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA

                object { // Anonymous object to hold calculated values
                    val sonMs = if (!status.isCharging) calculator.estimateTimeRemainingMillis(currentCapacityMah, positiveScreenOnRate) else 0L
                    val soffMs = if (!status.isCharging) calculator.estimateTimeRemainingMillis(currentCapacityMah, positiveScreenOffRate) else 0L
                    val mixRate = (positiveScreenOnRate * NewDischargeCalculator.MIXED_USAGE_SCREEN_ON_WEIGHT) +
                                  (positiveScreenOffRate * NewDischargeCalculator.MIXED_USAGE_SCREEN_OFF_WEIGHT)
                    val mixMs = if (!status.isCharging && mixRate > 0) calculator.estimateTimeRemainingMillis(currentCapacityMah, mixRate) else 0L
                    val son100Ms = if (!status.isCharging) calculator.estimateTimeRemainingMillis(effectiveCapacityMah, positiveScreenOnRate) else 0L
                    val soff100Ms = if (!status.isCharging) calculator.estimateTimeRemainingMillis(effectiveCapacityMah, positiveScreenOffRate) else 0L
                    val mix100Ms = if (!status.isCharging && mixRate > 0) calculator.estimateTimeRemainingMillis(effectiveCapacityMah, mixRate) else 0L
                }
            }.collect { times -> // This collect runs on Default dispatcher
                _uiState.update { // This update should be on Main if UI directly collects _uiState
                                  // Or if _uiState is collected via .flowOn(Dispatchers.Main)
                    it.copy(
                        screenOnTimeRemainingMs = times.sonMs,
                        screenOffTimeRemainingMs = times.soffMs,
                        mixedUsageTimeRemainingMs = times.mixMs,
                        screenOnTimeAt100PercentMs = times.son100Ms,
                        screenOffTimeAt100PercentMs = times.soff100Ms,
                        mixedUsageTimeAt100PercentMs = times.mix100Ms,
                        areTimeEstimationsLoading = false
                    )
                }
            }
        }
    }
    ```

*   **6. `ui/discharge/NewDischargeFragment.kt` (Enhancements):**
    *   In `onViewCreated`'s `collect` block:
        ```kotlin
        if (state.areTimeEstimationsLoading) {
            binding.dischargeSun.text = "..." // Placeholder
            // ... set placeholders for other time TextViews
            binding.dayBlock.visibility = View.GONE // or specific placeholder views
            binding.allBlock.visibility = View.GONE
            binding.nightBlock.visibility = View.GONE
            binding.indentDown.visibility = View.GONE
        } else {
            binding.dischargeSun.text = formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
            binding.dischargeAll.text = formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)
            binding.dischargeNight.text = formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)
            binding.dischargeFulltimeRemainingDay.text = formatMillisToHoursMinutes(state.screenOnTimeAt100PercentMs)
            binding.dischargeFulltimeRemainingAll.text = formatMillisToHoursMinutes(state.mixedUsageTimeAt100PercentMs)
            binding.dischargeFulltimeRemainingNight.text = formatMillisToHoursMinutes(state.screenOffTimeAt100PercentMs)

            binding.dayBlock.visibility = View.VISIBLE
            binding.allBlock.visibility = View.VISIBLE
            binding.nightBlock.visibility = View.VISIBLE
            binding.indentDown.visibility = View.VISIBLE
        }
        ```

*   **Test Cases (Phase 1):**
    1.  **TC1.1: Time Estimations with Cached/Default Rates:**
        *   Action: Launch fragment.
        *   Expected: Time estimation fields (`dischargeSun`, etc.) populate quickly, initially using cached or default discharge rates. Log rates used.
    2.  **TC1.2: Live Rate Learning Impact:**
        *   Action: Use device (screen on/off) to allow `NewBatteryRepository` to learn actual discharge rates.
        *   Expected: Time estimations gradually become more accurate as learned rates update. Log learned rates and subsequent re-estimations.

---

**Phase 2: Current Discharge Session Tracking (Async Updates, Persistence for Current Session)**

*   **Goal:** Implement logic to track and display data for the *current* discharge session in the "Current Session" block.
    *   **Performance:** Current session data is loaded from `CurrentSessionCache` on ViewModel init. Session updates are processed in `NewDischargeSessionRepository`'s background scope.
*   **New Kotlin Files:**
    *   `data/NewDischargeSessionData.kt`
    *   `cache/CurrentSessionCache.kt` (Interface)
    *   `cache/PrefsCurrentSessionCache.kt` (or Room implementation)
    *   `repository/NewDischargeSessionRepository.kt`
*   **Modified Kotlin Files:**
    *   `repository/NewBatteryRepository.kt` (triggers session updates in `NewDischargeSessionRepository`)
    *   `ui/discharge/NewDischargeViewModel.kt`
    *   `ui/discharge/NewDischargeFragment.kt`

*   **1. `data/NewDischargeSessionData.kt`:** (As in previous detailed plan)
*   **2. `cache/CurrentSessionCache.kt` & `PrefsCurrentSessionCache.kt`:** (As in previous detailed plan, using Gson for SharedPreferences)
*   **3. `repository/NewDischargeSessionRepository.kt`:** (As in previous detailed plan, ensuring `processBatteryStatus` and `endCurrentSessionAndCache` use `sessionScope` (IO dispatcher) and update `_currentSession` StateFlow, which is then saved to cache.)
*   **4. `repository/NewBatteryRepository.kt` (Enhancements):** (Ensure `batteryStatusFlow` collection calls `newDischargeSessionRepository.processBatteryStatus(status)`.)
*   **5. `ui/discharge/NewDischargeViewModel.kt` (Enhancements):**
    ```kotlin
    // ... (Inject dischargeSessionRepository: NewDischargeSessionRepository)
    init {
        // ... (existing init for basic info and time estimations)
        viewModelScope.launch {
            dischargeSessionRepository.currentSession.collect { session ->
                _uiState.update { it.copy(currentSession = session, isCurrentSessionLoading = session == null && it.isLoadingInitial) } // isLoading based on initial cache load
            }
        }
    }
    // onResetSessionClicked() will be fully fleshed out in Phase 3
    ```
*   **6. `ui/discharge/NewDischargeFragment.kt` (Enhancements):**
    *   In `onViewCreated`'s `collect` block for `viewModel.uiState`:
        ```kotlin
        if (state.isCurrentSessionLoading) {
            binding.currentSessionBlock.visibility = View.GONE // Or show placeholders within it
        } else {
            val session = state.currentSession
            if (session != null && !state.isCharging && session.isActive) { // Show active discharging session
                binding.currentSessionBlock.visibility = View.VISIBLE
                // ... (map session data to all TextViews within currentSessionBlock as detailed in previous plan's Phase 2)
                // e.g., binding.textFulltimeDisSession.text = formatMillisToHoursMinutes(session.durationMillis)
                // ... binding.dischargeSessionPercent.text = " (${session.startPercentage}% → ${session.currentPercentage}%)"
            } else if (session != null && !session.isActive) { // Show last completed session
                binding.currentSessionBlock.visibility = View.VISIBLE
                // ... (map data from the now finalized 'session' object)
            }
             else {
                binding.currentSessionBlock.visibility = View.GONE
            }
        }
        ```

*   **Test Cases (Phase 2):**
    1.  **TC2.1: Session Restore on App Restart:**
        *   Action: Start a discharge session, use for a bit. Close app (from recents). Reopen and navigate to fragment.
        *   Expected: The "Current Session" block displays data from the session that was active before app closure, loaded from cache.
    2.  **TC2.2: Session Data Accuracy & Updates:**
        *   Action: During an active discharge session, monitor values like duration, % dropped, mAh consumed, screen on/off specific stats.
        *   Expected: All fields update correctly and in a timely manner without freezing the UI.

---

**Phase 3: Historical Aggregate Statistics & Full Reset Button (Async Aggregation, Caching)**

*   **Goal:**
    *   Integrate with the existing (adapted) `DischargeSessionManager.kt` to store completed `NewDischargeSessionData` (after mapping to old `DischargeSession` format).
    *   Calculate and display aggregate statistics in the "Average Battery Usage" block.
    *   Implement the full "Reset Session" button functionality.
    *   **Performance:** Aggregate stats are calculated in the repository on a background thread, results are cached, and UI updates via `Flow`.
*   **Files to Adapt/New:**
    *   `manager/DischargeSessionManager.kt` (Adapt for Hilt DI, ensure `IO` dispatchers for its file ops).
    *   `manager/DischargeSession.kt` (Used by `DischargeSessionManager`).
    *   `cache/AggregateStatsCache.kt` (Interface & SharedPreferences/Room Impl. for `AggregateDischargeStats`).
*   **Modified Kotlin Files:**
    *   `repository/NewDischargeSessionRepository.kt` (saves to `DischargeSessionManager`, triggers aggregate refresh).
    *   `repository/NewBatteryRepository.kt` (manages `AggregateDischargeStats` flow, uses `AggregateStatsCache`).
    *   `ui/discharge/NewDischargeViewModel.kt`.
    *   `ui/discharge/NewDischargeFragment.kt`.

*   **1. Adapt `manager/DischargeSessionManager.kt`:**
    *   Add `@Inject constructor(...)` and ensure `PreferencesHelper` is injected.
    *   Modify `loadSessions()` and `saveSessions()` to run `withContext(Dispatchers.IO)`.
    *   Ensure methods like `getAllSessions()`, `getAverageSpeed()`, `clearSessions()` are public and thread-safe if called from multiple places (though repository should centralize this).

*   **2. `cache/AggregateStatsCache.kt`:**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.cache
    // ... (Define AggregateDischargeStats data class as in NewBatteryRepository below)
    import com.tqhit.battery.one.new_discharge.repository.NewBatteryRepository.AggregateDischargeStats 

    interface AggregateStatsCache {
        suspend fun getAggregateStats(): AggregateDischargeStats?
        suspend fun saveAggregateStats(stats: AggregateDischargeStats)
        suspend fun clear()
    }
    // Implement PrefsAggregateStatsCache using Gson for SharedPreferences
    ```

*   **3. `repository/NewDischargeSessionRepository.kt` (Enhancements):**
    ```kotlin
    // ... (Inject historicalSessionManager: DischargeSessionManager, batteryRepository: NewBatteryRepository)
    private suspend fun endCurrentSessionAndCache() { // Ensure this is suspend
        val sessionToFinalize = _currentSession.value
        if (sessionToFinalize != null && sessionToFinalize.isActive) {
            val finalizedNewSession = sessionToFinalize.copy(
                isActive = false,
                lastUpdateTimeEpochMillis = System.currentTimeMillis()
            )
            // Map NewDischargeSessionData to old DischargeSession
            val historicalSession = mapNewToOldDischargeSession(finalizedNewSession, batteryRepository.getEffectiveCapacityMah())
            
            withContext(Dispatchers.IO) { // Perform disk I/O off main thread
                historicalSessionManager.addSession(historicalSession)
            }
            
            _currentSession.value = finalizedNewSession // Update UI one last time
            currentSessionCache.clearCurrentSession() // Clear active session cache
            batteryRepository.notifyDischargeAggregatesChanged() // Signal repo to refresh aggregates
        }
    }

    suspend fun clearAllSessionData() {
        withContext(Dispatchers.IO) {
            historicalSessionManager.clearSessions()
        }
        clearActiveSessionData() // Clears current session cache and its StateFlow
        batteryRepository.notifyDischargeAggregatesChanged()
    }
    // mapNewToOldDischargeSession helper as in previous plan
    ```

*   **4. `repository/NewBatteryRepository.kt` (Enhancements):**
    ```kotlin
    // ... (Inject historicalSessionManager: DischargeSessionManager, aggregateStatsCache: AggregateStatsCache)
    private val _dischargeAggregatesUpdated = MutableSharedFlow<Unit>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)
    fun notifyDischargeAggregatesChanged() { _dischargeAggregatesUpdated.tryEmit(Unit) }

    data class AggregateDischargeStats( /* ... fields ... */ ) // As defined before

    val aggregateDischargeStatsFlow: Flow<AggregateDischargeStats> =
        _dischargeAggregatesUpdated.onStart { emit(Unit) } // Emit immediately on collection
            .flatMapLatest {
                flow {
                    aggregateStatsCache.getAggregateStats()?.let { emit(it) } // Emit cached first
                    val stats = withContext(Dispatchers.Default) { // Calculate on background
                        // Fetch all sessions from historicalSessionManager (which loads on IO)
                        val allSessions = historicalSessionManager.getAllSessions() // This call itself should be efficient
                        // Perform aggregation (average calculations)
                        // ... (logic to compute averages from allSessions)
                        AggregateDischargeStats(/* ... computed averages ... */)
                    }
                    aggregateStatsCache.saveAggregateStats(stats)
                    emit(stats)
                }
            }.shareIn(repositoryScope, SharingStarted.WhileSubscribed(5000), replay = 1)

    init {
        // ... (existing init for batteryStatusFlow, rates learning)
         _dischargeAggregatesUpdated.tryEmit(Unit) // Initial trigger for aggregates
    }
    ```

*   **5. `ui/discharge/NewDischargeViewModel.kt` (Enhancements):**
    ```kotlin
    // Add to NewDischargeUiState: aggregateStats, areAggregatesLoading
    init {
        // ...
        viewModelScope.launch {
            batteryRepository.aggregateDischargeStatsFlow.collect { stats ->
                _uiState.update { it.copy(aggregateStats = stats, areAggregatesLoading = false) }
            }
        }
    }

    fun onResetSessionDataClicked() {
        viewModelScope.launch {
            // The repository will handle clearing both current and historical, then notify for refresh
            dischargeSessionRepository.clearAllSessionData()
        }
    }
    ```

*   **6. `ui/discharge/NewDischargeFragment.kt` (Enhancements):**
    *   In `onViewCreated`'s `collect` block, handle `state.areAggregatesLoading`.
    *   Populate "Average Battery Usage" `TextViews` (e.g., `binding.dischargeSpeedPercentAll`, `binding.dischargeSpeedMahAll`, etc.) from `state.aggregateStats`.
    *   Make the "Average Battery Usage" block (`binding.averageInfo` container) visible.
    *   Wire up `binding.resetSessionDischargeButton` to `viewModel.onResetSessionDataClicked()`.

*   **Test Cases (Phase 3):**
    1.  **TC3.1: Aggregate Stats with Caching & Async Update:**
        *   Action: Complete several discharge sessions. Close and reopen the app/fragment.
        *   Expected: Aggregate stats load quickly (from cache if available), then may update if fresh calculation yields different results. UI remains responsive.
    2.  **TC3.2: Full Reset Button Clears All Data Persistently:**
        *   Action: Tap "Reset Session". Close and reopen app.
        *   Expected: All session-related UI (current and aggregate) shows default/empty states. `DischargeSessionManager`'s persisted data is cleared.

---

**Phase 4: UI Polish, Dialogs, and Info Buttons (Async Dialog Preparation)**

*   **Goal:** Implement UI animations, colored text styling, info button dialogs, and the low battery alarm dialog functionality, ensuring UI responsiveness.
    *   **Performance:** If dialogs require data that might be slow to fetch (e.g., complex settings for alarm dialog), this data should be prepared asynchronously by the ViewModel before the dialog is shown.
*   **New Kotlin Files:**
    *   `com.tqhit.battery.one.new_discharge.util.NewUiUtils.kt` (if not already created, for animations, text styling)
*   **Re-evaluate/Adapt Existing:**
    *   `dialog/utils/NotificationDialog.kt` (likely reusable for simple info).
    *   `dialog/alarm/SelectBatteryAlarmLowDialog.kt` (and its dependencies like `AppViewModel`, `VibrationService`).
        *   **Strategy:** Attempt to reuse by injecting `AppViewModel` and `VibrationService` into `NewDischargeFragment`. If this creates tight coupling or issues, a new, simpler dialog managed by `NewDischargeViewModel` will be created, potentially reading necessary settings from `AppRepository` via `NewDischargeViewModel`.
*   **Modified Kotlin Files:**
    *   `ui/discharge/NewDischargeFragment.kt`
    *   `ui/discharge/NewDischargeViewModel.kt` (manage state for dialogs if they require data preparation).

*   **1. `util/NewUiUtils.kt` (Example):**
    ```kotlin
    package com.tqhit.battery.one.new_discharge.util

    import android.animation.ValueAnimator
    import android.content.Context
    import android.text.Spannable
    import android.text.SpannableString
    import android.text.style.ForegroundColorSpan
    import android.widget.TextView
    import androidx.annotation.AttrRes
    import androidx.core.content.ContextCompat
    import com.tqhit.battery.one.R

    object NewUiUtils {
        fun animateTextViewPercentage(textView: TextView, from: Int, to: Int, duration: Long = 300) {
            // Ensure from and to are valid for animation if it's the first time
            val actualFrom = if (from < 0 || from > 100) to else from
            ValueAnimator.ofInt(actualFrom, to).apply {
                this.duration = duration
                addUpdateListener { animation ->
                    textView.text = "${animation.animatedValue as Int}%"
                }
                start()
            }
        }

        fun setColoredPercentageText(
            textView: TextView,
            context: Context,
            fullTextFormatResId: Int,
            percentage: Int,
            @AttrRes colorAttrResId: Int // Use attribute for themeable color
        ) {
            val themeColor = getThemeColor(context, colorAttrResId)
            val percentageStr = "$percentage%"
            val fullText = context.getString(fullTextFormatResId, percentage.toString())
            val start = fullText.indexOf(percentageStr)
            if (start != -1) {
                val end = start + percentageStr.length
                val spannable = SpannableString(fullText)
                spannable.setSpan(ForegroundColorSpan(themeColor), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                textView.text = spannable
            } else {
                textView.text = fullText 
            }
        }

        private fun getThemeColor(context: Context, @AttrRes colorAttr: Int): Int {
            val typedValue = android.util.TypedValue()
            context.theme.resolveAttribute(colorAttr, typedValue, true)
            return typedValue.data
        }
    }
    ```

*   **2. `ui/discharge/NewDischargeFragment.kt` (Enhancements):**
    ```kotlin
    // ... (Inject ApplovinInterstitialAdManager, AppViewModel, VibrationService, PermissionUtils)
    // ... (previousPercentage variable for animation)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        // ...
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    // ... (updateBasicInfo)
                    updateBasicInfo(state) // This now includes animation and colored text
                    updateTimeEstimations(state)
                    updateCurrentSessionDisplay(state)
                    updateAggregateStatsDisplay(state)
                    updateButtonVisibility(state) // Control visibility of all sections
                }
            }
        }
        setupInfoButtonListeners()
        setupAlarmButtonListener()
        setupResetButtonListener() // Add this
    }

    private fun updateBasicInfo(state: NewDischargeUiState) {
        if (state.isLoadingInitial && previousPercentage == -1) { // Only show placeholder if truly initial
            binding.dischargeTextPercent.text = getString(R.string.state_empty)
            binding.dischargeTextPercent3.text = getString(R.string.charging, "...")
        } else {
            if (previousPercentage != state.batteryPercentage && previousPercentage != -1) {
                NewUiUtils.animateTextViewPercentage(binding.dischargeTextPercent, previousPercentage, state.batteryPercentage)
            } else {
                binding.dischargeTextPercent.text = "${state.batteryPercentage}%"
            }
            previousPercentage = state.batteryPercentage

            NewUiUtils.setColoredPercentageText(
                binding.dischargeTextPercent3,
                requireContext(),
                R.string.charging, // e.g., "Charge at %1$s%%"
                state.batteryPercentage,
                R.attr.colorr // Your theme attribute for the highlight color
            )
        }
    }
    
    private fun updateTimeEstimations(state: NewDischargeUiState) {
        if (state.areTimeEstimationsLoading && binding.dischargeSun.text.contains("...")) { // Only show placeholder if truly initial for this section
            binding.dischargeSun.text = "..."
            // ... set placeholders for other time TextViews
        } else if (!state.areTimeEstimationsLoading) {
            binding.dischargeSun.text = DateTimeFormatters.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
            // ... update other time TextViews
        }
    }

    private fun updateCurrentSessionDisplay(state: NewDischargeUiState) {
        // ... (logic from previous phase to update current session views)
    }

    private fun updateAggregateStatsDisplay(state: NewDischargeUiState) {
        // ... (logic from previous phase to update aggregate views)
    }
    
    private fun updateButtonVisibility(state: NewDischargeUiState) {
        // Control visibility based on loading states and data presence
        binding.dayBlock.visibility = if (state.areTimeEstimationsLoading || state.isLoadingInitial) View.GONE else View.VISIBLE
        // ... similar for all_block, night_block, indent_down (for 100% estimates)

        binding.currentSessionBlock.visibility = if (state.isCurrentSessionLoading || state.isLoadingInitial || state.currentSession == null || state.isCharging) View.GONE else View.VISIBLE
        // ... ensure child info buttons are also managed

        binding.averageInfo.visibility = if (state.areAggregatesLoading || state.isLoadingInitial) View.GONE else View.VISIBLE
        
        binding.batteryAlarmBtn.visibility = if(state.isLoadingInitial) View.GONE else View.VISIBLE
        binding.resetSessionDischargeButton.visibility = if(state.isLoadingInitial) View.GONE else View.VISIBLE

    }

    private fun setupResetButtonListener() {
        binding.resetSessionDischargeButton.setOnClickListener {
            viewModel.onResetSessionDataClicked()
        }
    }
    // ... (setupInfoButtonListeners, showInfoDialogWithAd, permissionLauncher, setupAlarmButtonListener, showLowBatteryAlarmDialog as in previous plan)
    ```

*   **Test Cases (Phase 4):**
    1.  **TC4.1: UI Animations & Styling:**
        *   Action: Observe `binding.dischargeTextPercent` and `binding.dischargeTextPercent3`.
        *   Expected: Percentage animates smoothly. Highlighted text is correctly colored.
    2.  **TC4.2: Info Dialogs Responsiveness:**
        *   Action: Tap info buttons.
        *   Expected: Dialogs (and ads, if any) appear without noticeable UI lag.
    3.  **TC4.3: Low Battery Alarm Dialog:**
        *   Action: Tap alarm button.
        *   Expected: Dialog flow (permission, ad, dialog display) is smooth and functional.