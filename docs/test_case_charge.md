Here are unit tests I'd suggest, focusing on isolating each part of the chain:

**I. Testing the Calculation Logic Directly (within ViewModel and NotificationManager)**

Even though we confirmed the formula, testing it with various inputs ensures no typos or subtle logic errors.

*   **`NewChargeViewModelTest.kt`**
    *   **Purpose:** Verify that `createUiState` (or the part of it that calculates `powerText`) produces the correct string output given various `NewChargeStatus` inputs.
    *   **Setup:**
        *   Mock `AppRepository` (if its methods are called within the power calculation path, though it seems not for power itself).
        *   Mock `Context` (for string resources if any, but `String.format` doesn't need it).
        *   You'll be directly creating `NewChargeStatus` objects.
    *   **Test Cases for `powerText` generation:**
        *   `test_charging_positiveCurrentAndVoltage_calculatesCorrectPowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 2000000L (2A), voltageMillivolts = 5000 (5V), isCharging = true, ...)`
            *   Expected `powerText`: `"10.00 W"`
        *   `test_charging_zeroCurrent_showsDashPowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 0L, voltageMillivolts = 5000, isCharging = true, ...)`
            *   Expected `powerText`: `"-"`
        *   `test_charging_zeroVoltage_showsDashPowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 1000000L, voltageMillivolts = 0, isCharging = true, ...)`
            *   Expected `powerText`: `"-"`
        *   `test_charging_verySmallCurrentAndVoltage_calculatesCorrectPowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 1000L (1mA), voltageMillivolts = 1000 (1V), isCharging = true, ...)`
            *   Expected `powerText`: `"0.00 W"` (or `"0.001 W"` if precision was higher, but your formula implies division resulting in float then formatting to `%.2f`) Let's re-check the formula: `(1000/1000.0f) * (1000/1000.0f) / 1000.0f = 1.0f * 1.0f / 1000.0f = 0.001f`. Formatted to `%.2f` becomes `"0.00 W"`.
        *   `test_discharging_negativeCurrent_calculatesNegativePowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = -500000L (-0.5A), voltageMillivolts = 3800 (3.8V), isCharging = false, ...)`
            *   Expected `powerText`: `"-1.90 W"`
        *   `test_notCharging_butValidCurrentVoltage_calculatesPowerText()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 100000L (0.1A), voltageMillivolts = 4000 (4V), isCharging = false, ...)`
            *   Expected `powerText`: ` "0.40 W"` (The UI will then decide what to show based on `isCharging` flag, but the raw calculation should still work)

*   **`ChargeNotificationManagerTest.kt`**
    *   **Purpose:** Verify the power calculation embedded in the notification content string.
    *   **Setup:**
        *   Mock `Context` (for string resources like `R.string.charge`).
        *   Mock `AppRepository` (for `isVibrationEnabled`, etc., if those paths are triggered).
    *   **Test Cases for power in `createServiceNotification` content:**
        *   `test_serviceNotification_positiveCurrentAndVoltage_showsCorrectPowerInContent()`
            *   Input: `NewChargeStatus(currentMicroAmperes = 1500000L, voltageMillivolts = 5000, temperatureCelsius = 25.0f, ...)`
            *   Expected content substring: `(7.5 W)` (since power is `1.5 * 5.0 * 1000 / 1000 = 7.5`)
            *   Assert that the formatted string in `notification.extras.getCharSequence(NotificationCompat.EXTRA_TEXT).toString()` contains the expected power.

**II. Testing the Data Source (`AndroidBatteryStatsDataSource.kt`)**

*   **`AndroidBatteryStatsDataSourceTest.kt`**
    *   **Purpose:** Verify that `NewChargeStatus` is correctly populated from a mocked `Intent` (for `onReceive`) and `BatteryManager` (for `BATTERY_PROPERTY_CURRENT_NOW`).
    *   **Setup:**
        *   Mock `Context`.
        *   Mock `Intent`.
        *   Mock `BatteryManager`.
        *   Use `ApplicationProvider.getApplicationContext()` if you need a real context for `getSystemService`.
    *   **Test Cases:**
        *   `test_onReceive_validBatteryChangedIntent_emitsCorrectChargeStatus()`
            *   Setup mock `Intent` with `ACTION_BATTERY_CHANGED`.
            *   Put extras: `EXTRA_STATUS = BATTERY_STATUS_CHARGING`, `EXTRA_PLUGGED = BATTERY_PLUGGED_AC`, `EXTRA_LEVEL = 50`, `EXTRA_SCALE = 100`, `EXTRA_VOLTAGE = 4200`, `EXTRA_TEMPERATURE = 300`.
            *   Mock `context.getSystemService(Context.BATTERY_SERVICE)` to return your mock `BatteryManager`.
            *   Mock `batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)` to return `1500000L`.
            *   Call `onReceive` (you might need to expose it or test through the flow).
            *   Assert the emitted `NewChargeStatus` has `percentage = 50`, `isCharging = true`, `pluggedSource = BATTERY_PLUGGED_AC`, `currentMicroAmperes = 1500000L`, `voltageMillivolts = 4200`, `temperatureCelsius = 30.0f`.
        *   `test_onReceive_currentNowIsZero_emitsCorrectChargeStatus()`
            *   Similar to above, but mock `batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)` to return `0L`.
            *   Assert `currentMicroAmperes = 0L`.
        *   `test_onReceive_voltageIsZero_emitsCorrectChargeStatus()`
            *   Similar, but `EXTRA_VOLTAGE = 0`.
            *   Assert `voltageMillivolts = 0`.
        *   **Critical Comparison:**
            *   Look at `new_discharge/datasource/AndroidBatteryDataSource.kt`. It has this logic:
                ```kotlin
                val _currentNowMicroAmperes = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
                val currentNowMicroAmperes = if(abs(_currentNowMicroAmperes) < 1000){ // If value is small, assume it's mA
                    _currentNowMicroAmperes.toDouble() * 1000
                } else {
                    _currentNowMicroAmperes
                }
                ```
            *   Your `charge/datasource/AndroidBatteryStatsDataSource.kt` **does not** have this. It directly uses the value:
                ```kotlin
                val currentMicroAmp = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
                    batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW).toLong()
                } else {
                    0L // This else branch is likely dead code on modern devices
                }
                ```
            *   **This is a HUGE potential source of the error.** If `BATTERY_PROPERTY_CURRENT_NOW` on some devices returns milliamperes (mA) instead of microamperes (µA), your `charge` module would interpret it as µA, making the current value 1000 times smaller, and thus the power 1000 times smaller.
            *   **Test Case for this potential issue:**
                *   `test_onReceive_currentNowIsSmallPositive_emitsValueAsIs()`
                    *   Mock `batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)` to return `500L`. (Assuming this is µA).
                    *   Assert `currentMicroAmperes = 500L` in the `NewChargeStatus`.
                *   `test_onReceive_currentNowIsSmallNegative_emitsValueAsIs()`
                    *   Mock `batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)` to return `-500L`.
                    *   Assert `currentMicroAmperes = -500L`.