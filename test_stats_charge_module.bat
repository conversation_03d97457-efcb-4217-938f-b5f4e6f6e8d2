@echo off
REM StatsChargeModule Device Testing Script
REM Phase 2: Device Testing & Verification

set ADB_PATH="E:\IDE\Android\SDK\platform-tools\adb.exe"
set PACKAGE_NAME=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
set MAIN_ACTIVITY=%PACKAGE_NAME%/.activity.splash.SplashActivity

echo ========================================
echo StatsChargeModule Device Testing Script
echo ========================================
echo.

REM Step 1: Check for connected devices
echo [STEP 1] Checking for connected devices...
%ADB_PATH% devices
if %ERRORLEVEL% neq 0 (
    echo ERROR: ADB command failed
    pause
    exit /b 1
)

REM Check if any device is connected
for /f "skip=1 tokens=1" %%i in ('%ADB_PATH% devices') do (
    if not "%%i"=="" (
        set DEVICE_FOUND=1
        goto device_found
    )
)

echo No devices connected. Please connect a device and run this script again.
pause
exit /b 1

:device_found
echo Device found! Proceeding with testing...
echo.

REM Step 2: Install the APK
echo [STEP 2] Installing StatsChargeModule APK...
%ADB_PATH% install -r %APK_PATH%
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to install APK
    pause
    exit /b 1
)
echo APK installed successfully!
echo.

REM Step 3: Clear previous logs
echo [STEP 3] Clearing previous logs...
%ADB_PATH% logcat -c
echo Logs cleared.
echo.

REM Step 4: Start log monitoring in background
echo [STEP 4] Starting log monitoring...
echo Starting background log monitoring for StatsChargeModule...
start "StatsCharge Logs" cmd /k "%ADB_PATH% logcat | findstr /i \"StatsCharge CoreBattery\""
timeout /t 2 /nobreak >nul

REM Step 5: Launch the app
echo [STEP 5] Launching the app...
%ADB_PATH% shell am start -n %MAIN_ACTIVITY%
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to launch app
    pause
    exit /b 1
)
echo App launched successfully!
echo.

REM Step 6: Wait for app to initialize
echo [STEP 6] Waiting for app initialization...
timeout /t 5 /nobreak >nul

REM Step 7: Check if StatsChargeFragment is loaded
echo [STEP 7] Checking StatsChargeFragment status...
%ADB_PATH% logcat -d | findstr /i "StatsChargeFragment"
echo.

REM Step 8: Display current battery status
echo [STEP 8] Current battery status:
%ADB_PATH% shell dumpsys battery
echo.

echo ========================================
echo Testing Instructions:
echo ========================================
echo 1. The app should now be running with StatsChargeFragment
echo 2. Check the log window for StatsChargeModule activity
echo 3. Try plugging/unplugging charger to test session tracking
echo 4. Observe time estimates and target percentage functionality
echo 5. Test the reset session button
echo.
echo Log monitoring is running in a separate window.
echo Press any key to continue with automated tests...
pause >nul

REM Step 9: Automated battery status simulation
echo [STEP 9] Testing battery status changes...
echo Simulating battery level changes for testing...

REM Get current battery level
for /f "tokens=2 delims= " %%i in ('%ADB_PATH% shell dumpsys battery ^| findstr "level"') do set CURRENT_LEVEL=%%i
echo Current battery level: %CURRENT_LEVEL%

echo.
echo Manual Testing Phase:
echo - Plug in charger and observe session start logs
echo - Unplug charger and observe session end logs  
echo - Change target percentage and verify estimates update
echo - Test reset session functionality
echo - Close and reopen app to test session persistence
echo.
echo Press any key when manual testing is complete...
pause >nul

echo ========================================
echo Testing Complete!
echo ========================================
echo Check the log window for detailed StatsChargeModule activity.
echo.
pause
