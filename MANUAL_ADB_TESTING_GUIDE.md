# TJ_BatteryOne Notification Service Optimization - Manual ADB Testing Guide

## Prerequisites
- **ADB Path**: `E:\IDE\Android\SDK\platform-tools\adb.exe`
- **Bundle ID**: `com.fc.p.tj.charginganimation.batterycharging.chargeeffect`
- **Android Device**: Connected with USB debugging enabled
- **TJ_BatteryOne App**: Installed and running on the device

## Step-by-Step Testing Instructions

### 1. Device Connection Verification

```bash
# Check if device is connected
E:\IDE\Android\SDK\platform-tools\adb.exe devices

# Expected output: Should show your device listed as "device"
# Example: ABC123456789    device
```

### 2. Service Consolidation Verification

```bash
# Check which battery services are running
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "BatteryStatsService\|BatteryMonitorService\|ChargeMonitorService"

# Expected Results:
# ✅ CoreBatteryStatsService should be present
# ❌ BatteryStatusService should NOT be present
# ❌ BatteryMonitorService should NOT be present  
# ❌ NewChargeMonitorService should NOT be present
```

**Verification Commands:**
```bash
# Check for CoreBatteryStatsService (should be running)
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "CoreBatteryStatsService"

# Check for legacy services (should NOT be running)
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "BatteryStatusService"
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "BatteryMonitorService"
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "NewChargeMonitorService"
```

### 3. Real-Time Notification Monitoring

```bash
# Monitor notification updates with timing information
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D"

# Look for these key messages:
# - "Updating service notification (interval: XXXXms)"
# - "Generated notification content"
# - "Forcing notification update"
# - "Screen turned on/off"
```

### 4. Adaptive Update Interval Testing

**Manual Test Procedure:**
1. **Start monitoring**: Run the logcat command above
2. **Screen ON + Charging**: Connect charger with screen on
   - **Expected**: Updates every ~15 seconds
3. **Screen OFF**: Turn screen off while charging
   - **Expected**: Updates every ~60 seconds
4. **Screen ON + Stable**: Disconnect charger, turn screen on
   - **Expected**: Updates every ~45 seconds

**Monitoring Command:**
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "interval\|Screen\|Updating service notification"
```

### 5. Content Caching Verification

```bash
# Monitor content generation and caching
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "ChargeNotificationManager:V" | findstr "Generated notification content\|cached"

# Expected Behavior:
# - First update: "Generated notification content: Now: X.X mA..."
# - Subsequent identical status: Should use cached content (fewer log entries)
```

### 6. Forced Update Testing

**Manual Test Procedure:**
1. **Monitor forced updates**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "Forcing notification update"
```

2. **Trigger forced updates**:
   - Connect/disconnect charger (charging state change)
   - Wait for battery percentage to change by 1%
   - Use a high-power app to change current draw significantly

**Expected Messages:**
- "Forcing notification update - charging state changed"
- "Forcing notification update - significant percentage change: X%"
- "Forcing notification update - significant current change: XmA"

### 7. Performance Verification

```bash
# Check app memory usage
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys meminfo com.fc.p.tj.charginganimation.batterycharging.chargeeffect

# Check CPU usage
E:\IDE\Android\SDK\platform-tools\adb.exe shell top -n 3 | findstr "com.fc.p.tj.charginganimation"

# Monitor battery status
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys battery
```

### 8. Notification Content Accuracy Testing

```bash
# Monitor notification content details
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "ChargeNotificationManager:D" | findstr "Now:"

# Expected Content Format:
# "Now: X.X mA (X.X W) • Temp: X.X°C ⚡" (when charging)
# "Now: X.X mA (X.X W) • Temp: X.X°C" (when not charging)
```

### 9. Target Percentage Alert Testing

**Manual Test Procedure:**
1. **Set target percentage** in the TJ_BatteryOne app (e.g., 80%)
2. **Monitor alerts**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D" | findstr "Target\|reached\|showTargetChargeReachedNotification"
```
3. **Charge device** towards the target percentage
4. **Verify alert** appears when target is reached

### 10. Charging State Change Testing

**Manual Test Procedure:**
1. **Monitor state changes**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "Charging state\|started charging\|stopped charging"
```
2. **Connect charger** - should see "charging started" notification
3. **Disconnect charger** - should see "charging stopped" notification

### 11. App Startup Performance

```bash
# Measure app startup time
E:\IDE\Android\SDK\platform-tools\adb.exe shell am start -W com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity

# Expected output includes timing information:
# ThisTime: XXX (time for this activity)
# TotalTime: XXX (total startup time)
# WaitTime: XXX (time including system delays)
```

## Success Criteria Checklist

### ✅ Service Consolidation
- [ ] Only CoreBatteryStatsService is running
- [ ] No legacy services (BatteryStatusService, BatteryMonitorService, NewChargeMonitorService) are running

### ✅ Adaptive Update Intervals
- [ ] 15-second intervals when charging
- [ ] 60-second intervals when screen is off
- [ ] 45-second intervals in stable state

### ✅ Content Caching
- [ ] Identical battery status uses cached content
- [ ] New status generates fresh content
- [ ] Reduced CPU usage from string operations

### ✅ Forced Updates
- [ ] Immediate updates for charging state changes
- [ ] Updates for ≥1% battery percentage changes
- [ ] Updates for ≥100mA current changes

### ✅ Performance
- [ ] Memory usage is reasonable (<20MB)
- [ ] CPU usage is minimal (<1% during normal operation)
- [ ] No memory leaks or crashes

### ✅ Notification Content
- [ ] Accurate current (mA) display
- [ ] Correct power (W) calculation
- [ ] Proper temperature (°C) reading
- [ ] Charging indicator (⚡) when charging

### ✅ User Experience
- [ ] Target percentage alerts work
- [ ] Charging state notifications appear
- [ ] Screen state optimization functions
- [ ] No notification delays or glitches

## Troubleshooting

### If CoreBatteryStatsService is not found:
1. Restart the TJ_BatteryOne app
2. Check if the app has proper permissions
3. Verify the app is the correct version with optimizations

### If legacy services are still running:
1. Force stop the app: `adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect`
2. Restart the app
3. Check MainActivity service startup configuration

### If notifications are not updating:
1. Check notification permissions for the app
2. Verify the app is not in battery optimization whitelist
3. Check if Do Not Disturb mode is affecting notifications

### If performance is poor:
1. Clear app cache: `adb shell pm clear com.fc.p.tj.charginganimation.batterycharging.chargeeffect`
2. Restart the device
3. Check for other battery-intensive apps running

## Log Collection for Analysis

```bash
# Collect comprehensive logs for analysis
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -d > tj_battery_notification_logs.txt

# Filter for specific components
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -d -s "UnifiedBatteryNotificationService" "ChargeNotificationManager" "CoreBatteryStatsService" > filtered_logs.txt
```

This comprehensive testing will verify that all notification service optimizations are working correctly and provide measurable performance improvements.
